import csv

def extract_first_line_from_csv(file_path):
    with open(file_path, mode='r', newline='') as file:
        reader = csv.reader(file)
        for row in reader:
            if row:  # Check if the row is not empty
                first_line = row[0].split('\n')[0]
                print(first_line)

# Specify the path to your CSV file
csv_file_path = 'status.csv'
extract_first_line_from_csv(csv_file_path)