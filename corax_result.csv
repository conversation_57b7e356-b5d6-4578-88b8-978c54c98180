AccessPathChainAlias_004_F.java,,false
AccessPathChainAlias_002_F.java,,false
PropertyIsTaintOrNot_Object_002_F.java,,false
AccessPathChainAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/AccessPathChainAlias_001_T.java,true
PropertyIsTaintOrNot_Object_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/PropertyIsTaintOrNot_Object_001_T.java,true
AccessPathChainAlias_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/AccessPathChainAlias_003_T.java,true
MapPutGet_003_T.java,,false
Array_index_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_muilt_collection/Array_index_003_T.java,true
MapPutGet_004_F.java,,false
Array_index_004_F.java,,false
ArrayOutOfBoundOrNot_004_F.java,,false
ArrayOutOfBoundOrNot_002_F.java,,false
Array_index_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/array/Array_index_001_T.java,true
ArrayOutOfBoundOrNot_003_T.java,,false
Array_index_002_F.java,,false
ArrayOutOfBoundOrNot_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/array/ArrayOutOfBoundOrNot_001_T.java,true
MapField_005_T.java,,false
MapField_007_T.java,,false
MapPutGet_001_T.java,,false
MapField_003_T.java,,false
MapField_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/map/MapField_001_T.java,true
QueueWithLambda_001_T.java,,false
QueueWithLambda_002_F.java,,false
MapField_002_F.java,,false
MapField_006_F.java,,false
MapField_008_F.java,,false
MapField_004_F.java,,false
MapPutGet_002_F.java,,false
ObjectDiffAttribute_002_F.java,,false
ObjectDiffAttribute_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/interface_class/ObjectDiffAttribute_001_T.java,true
Map_obj_sensitive_005_T.java,,false
Map_obj_sensitive_003_T.java,,false
Map_obj_sensitive_001_T.java,,false
Map_obj_sensitive_002_F.java,,false
Map_obj_sensitive_006_F.java,,false
Map_obj_sensitive_004_F.java,,false
ArrayElementOverwrite_002_F.java,,false
ArrayElementOverwrite_004_F.java,,false
ArrayElementOverwrite_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/set_array/ArrayElementOverwrite_003_T.java,true
ArrayElementOverwrite_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/set_array/ArrayElementOverwrite_001_T.java,true
DifferentParamsForFunction_002_F.java,,false
DifferentParamsForFunction_006_F.java,,false
MultiCallSite_002_F.java,,false
DifferentParamsForFunction_004_F.java,,false
DifferentParamsForFunction_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/argument_value_passing/DifferentParamsForFunction_005_T.java,true
MultiCallSite_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/MultiCallSite_001_T.java,true
DifferentParamsForFunction_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/DifferentParamsForFunction_003_T.java,true
DifferentParamsForFunction_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/DifferentParamsForFunction_001_T.java,true
Expression_Polymorphism_002_F.java,,false
Expression_Polymorphism_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/polymorphism/Expression_Polymorphism_001_T.java,true
return_value_passing_002_F.java,,false
return_value_passing_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/return_value_passing/return_value_passing_001_T.java,true
Expression_MethodInvocation_InfixExpression_002_F.java,,false
DifferentParamsForFunction_006_F.java,,false
Expression_CallExpression_Package_001_T.java,,false
Expression_MethodInvocation_001_T.java,,false
DifferentParamsForFunction_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/argument_value_passing/DifferentParamsForFunction_005_T.java,true
Expression_MethodInvocation_InfixExpression_001_T.java,,false
Expression_MethodInvocation_002_F.java,,false
Expression_CallExpression_Package_002_F.java,,false
Statement_InterruptStatement_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_002_F.java,true
Statement_InterruptStatement_006_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_006_F.java,true
Statement_InterruptStatement_008_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_008_F.java,true
Statement_InterruptStatement_004_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_004_F.java,true
Statement_InterruptStatement_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_005_T.java,true
Statement_InterruptStatement_007_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_007_T.java,true
Statement_InterruptStatement_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_003_T.java,true
Statement_InterruptStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_001_T.java,true
ConstantIfGuard_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/no_solver/ConstantIfGuard_001_T.java,true
Statement_ForStatement_004_F.java,,false
Statement_ForStatement_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/no_solver/Statement_ForStatement_003_T.java,true
ConstantIfGuard_002_F.java,,false
Statement_AssertStatement_006_F.java,,false
Statement_AssertStatement_004_F.java,,false
Statement_WhileStatement_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_WhileStatement_003_T.java,true
DifferentIfBranch_ArrayLength_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_002_F.java,true
Expression_InstanceofExpression_004_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Expression_InstanceofExpression_004_F.java,true
Expression_InstanceofExpression_006_F.java,,false
Statement_WhileStatement_004_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_WhileStatement_004_F.java,true
Statement_AssertStatement_003_T.java,,false
Expression_InstanceofExpression_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Expression_InstanceofExpression_005_T.java,true
Statement_AssertStatement_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_AssertStatement_005_T.java,true
DifferentIfBranch_ArrayLength_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_003_T.java,true
Expression_InstanceofExpression_003_T.java,,false
DifferentIfBranch_ArrayLength_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_001_T.java,true
Statement_TryStatement_006_F.java,,false
Statement_TryStatement_004_F.java,,false
Statement_TryStatement_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_002_F.java,true
Statement_TryStatement_003_T.java,,false
Statement_TryStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_001_T.java,true
Statement_TryStatement_005_T.java,,false
CompletableFuture_002_F.java,,false
Async_Multithreading_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/asynchronous/Async_Multithreading_003_T.java,true
Async_Multithreading_001_T.java,,false
Async_Multithreading_009_T.java,,false
Async_Multithreading_005_T.java,,false
Async_Multithreading_007_T.java,,false
Async_Multithreading_006_F.java,,false
Async_Multithreading_008_F.java,,false
Async_Multithreading_004_F.java,,false
CompletableFuture_001_T.java,,false
Async_Multithreading_010_F.java,,false
Async_Multithreading_002_F.java,,false
FlowSensitiveAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/FlowSensitiveAlias_001_T.java,true
AssignedByVariable_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignedByVariable_002_T.java,true
AssignObjectAttribute_001_F.java,,false
AssignedByFixedValue_001_F.java,,false
FlowSensitiveAlias_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/FlowSensitiveAlias_003_T.java,true
FlowSensitiveAlias_004_F.java,,false
AssignedByVariable_001_F.java,,false
FlowSensitiveAlias_002_F.java,,false
AssignObjectAttribute_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignObjectAttribute_002_T.java,true
AssignedByFixedValue_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignedByFixedValue_002_T.java,true
Statement_ForStatement_008_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_008_F.java,true
Statement_ForStatement_006_F.java,,false
Statement_ForStatement_007_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_007_T.java,true
Statement_ForStatement_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_005_T.java,true
simple_object_002_F.java,,false
simple_object_001_T.java,,false
CallExpression_CustomCode_Class_007_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/anonymous_object/CallExpression_CustomCode_Class_007_T.java,true
CallExpression_CustomCode_Class_008_F.java,,false
CallExpression_CustomCode_Class_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/abstract_class/CallExpression_CustomCode_Class_005_T.java,true
CallExpression_CustomCode_Class_006_F.java,,false
CallExpression_CustomCode_Class_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/complex_object/CallExpression_CustomCode_Class_001_T.java,true
CallExpression_CustomCode_Class_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/complex_object/CallExpression_CustomCode_Class_003_T.java,true
CallExpression_CustomCode_Interface_003_T.java,,false
CallExpression_CustomCode_Class_004_F.java,,false
CallExpression_CustomCode_Interface_004_F.java,,false
CallExpression_CustomCode_Class_002_F.java,,false
ReturnAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/ReturnAlias_001_T.java,true
FieldUnAlias_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/FieldUnAlias_002_T.java,true
InnerClassAlias_006_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_006_T.java,true
InnerClassAlias_002_F.java,,false
SingleFieldAccessAlias_002_F.java,,false
InnerClassAlias_010_F.java,,false
StaticFieldAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/StaticFieldAlias_001_T.java,true
HeapOverwriteAlias_002_F.java,,false
ReturnAlias_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/ReturnAlias_003_T.java,true
FieldUnAlias_004_F.java,,false
NullAlias_002_F.java,,false
InnerClassAlias_004_F.java,,false
InnerClassAlias_008_F.java,,false
HeapPointsToSelfAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapPointsToSelfAlias_001_T.java,true
PrimitiveFieldAccess_001_T.java,,false
PrimitiveFieldAccess_003_T.java,,false
HeapOverwriteAlias_004_F.java,,false
SameArgumentAlias_002_F.java,,false
InnerClassAlias_003_F.java,,false
InnerClassAlias_007_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_007_T.java,true
ReturnAlias_004_F.java,,false
FieldUnAlias_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/FieldUnAlias_003_T.java,true
HeapPointsToSelfAlias_002_F.java,,false
PrimitiveFieldAccess_002_F.java,,false
SameArgumentAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/SameArgumentAlias_001_T.java,true
InnerClassAlias_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_005_T.java,true
InnerClassAlias_009_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_009_T.java,true
FieldUnAlias_001_F.java,,false
PrimitiveFieldAccess_004_F.java,,false
ReturnAlias_002_F.java,,false
HeapOverwriteAlias_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapOverwriteAlias_003_T.java,true
InnerClassAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_001_T.java,true
SingleFieldAccessAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/SingleFieldAccessAlias_001_T.java,true
StaticFieldAlias_002_F.java,,false
HeapOverwriteAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapOverwriteAlias_001_T.java,true
NullAlias_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/NullAlias_001_T.java,true
CompletableFuture_002_F.java,,false
CompletableFuture_004_F.java,,false
CompletableFuture_001_T.java,,false
CompletableFuture_003_T.java,,false
Async_Multithreading_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/asynchronous/Async_Multithreading_003_T.java,true
Async_Multithreading_001_T.java,,false
Async_Multithreading_009_T.java,,false
Async_Multithreading_005_T.java,,false
Async_Multithreading_007_T.java,,false
Async_Multithreading_006_F.java,,false
Async_Multithreading_008_F.java,,false
Async_Multithreading_004_F.java,,false
Async_Multithreading_010_F.java,,false
Async_Multithreading_002_F.java,,false
cross_file_001_T_a.java,,false
cross_file_001_T_b.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/cross_file_package_namespace/cross_file/cross_file_001_T/cross_file_001_T_b.java,true
cross_file_002_F_b.java,,false
cross_file_002_F_a.java,,false
cross_directory_002_F_a.java,,false
cross_directory_002_F_b.java,,false
cross_directory_001_T_a.java,,false
cross_directory_001_T_b.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/cross_file_package_namespace/cross_directory/cross_directory_001_T/B/cross_directory_001_T_b.java,true
Expression_MethodInvocation_Argument_001_F.java,,false
Expression_MethodInvocation_Argument_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/chained_call/Expression_MethodInvocation_Argument_002_T.java,true
Expression_Polymorphism_002_F.java,,false
Expression_Polymorphism_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/polymorphism/Expression_Polymorphism_001_T.java,true
static_method_002_F.java,,false
static_field_002_F.java,,false
static_field_001_T.java,,false
static_method_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/static_method/static_method_001_T.java,true
return_value_passing_002_F.java,,false
return_value_passing_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/return_value_passing/return_value_passing_001_T.java,true
Expression_MethodInvocation_InfixExpression_002_F.java,,false
Expression_CallExpression_Package_001_T.java,,false
Expression_MethodInvocation_001_T.java,,false
Expression_MethodInvocation_InfixExpression_001_T.java,,false
Expression_MethodInvocation_002_F.java,,false
Expression_CallExpression_Package_002_F.java,,false
higher_order_function_001_T.java,,false
higher_order_function_002_F.java,,false
CallExpression_NoSourceCode_Native_003_F.java,,false
CallExpression_CustomCode_Interface_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_CustomCode_Interface_001_T.java,true
CallExpression_NoSourceCode_Native_001_F.java,,false
Expression_CallExpression_Array_002_T.java,,false
Expression_MethodInvocation_MethodInvocation_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/Expression_MethodInvocation_MethodInvocation_002_T.java,true
CallExpression_NoSourceCode_Native_005_F.java,,false
CallExpression_NoSourceCode_Native_006_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_006_T.java,true
CallExpression_NoSourceCode_Native_004_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_004_T.java,true
CallExpression_CustomCode_Interface_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_CustomCode_Interface_002_F.java,true
Expression_CallExpression_Array_001_F.java,,false
Expression_MethodInvocation_MethodInvocation_001_F.java,,false
CallExpression_NoSourceCode_Native_002_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_002_T.java,true
CallExpression_CustomCode_Class_011_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/generics/CallExpression_CustomCode_Class_011_T.java,true
CallExpression_CustomCode_Class_009_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/generics/CallExpression_CustomCode_Class_009_T.java,true
CallExpression_CustomCode_Class_010_F.java,,false
CallExpression_CustomCode_Class_012_F.java,,false
Base_ArrayAccess_006_F.java,,false
Base_ArrayAccess_008_F.java,,false
Base_ArrayAccess_004_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_004_F.java,true
Base_ArrayAccess_010_F.java,,false
Base_ArrayAccess_002_F.java,,false
Base_ArrayAccess_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_003_T.java,true
Base_ArrayAccess_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_001_T.java,true
Base_ArrayAccess_009_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_009_T.java,true
Base_ArrayAccess_005_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_005_T.java,true
Base_ArrayAccess_007_T.java,,false
Base_Byte_006_F.java,,false
Base_Char_001_T.java,,false
Base_Long_001_T.java,,false
Base_Double_004_F.java,,false
Base_Byte_008_F.java,,false
Base_Long_003_T.java,,false
Base_Char_003_T.java,,false
Base_Byte_004_F.java,,false
Base_ByteArray_004_F.java,,false
Base_Integer_002_F.java,,false
Base_Double_002_F.java,,false
Base_Char_007_T.java,,false
Base_Long_007_T.java,,false
Base_Float_003_T.java,,false
Base_CharArray_001_T.java,,false
Base_Integer_006_F.java,,false
Base_Integer_004_F.java,,false
Base_CharArray_003_T.java,,false
Base_Float_001_T.java,,false
Base_Integer_008_F.java,,false
Base_ByteArray_002_F.java,,false
Base_Long_005_T.java,,false
Base_Byte_002_F.java,,false
Base_Char_005_T.java,,false
Base_Long_004_F.java,,false
Base_Char_008_F.java,,false
Base_Byte_003_T.java,,false
Base_Char_004_F.java,,false
Base_Long_008_F.java,,false
Base_Double_001_T.java,,false
Base_ByteArray_003_T.java,,false
Base_CharArray_002_F.java,,false
Base_Integer_005_T.java,,false
Base_Float_002_F.java,,false
Base_Integer_007_T.java,,false
Base_Char_006_F.java,,false
Base_Byte_001_T.java,,false
Base_Long_006_F.java,,false
Base_Double_003_T.java,,false
Base_ByteArray_001_T.java,,false
Base_Integer_003_T.java,,false
Base_CharArray_004_F.java,,false
Base_Long_002_F.java,,false
Base_Char_002_F.java,,false
Base_Byte_005_T.java,,false
Base_Byte_007_T.java,,false
Base_Integer_001_T.java,,false
Base_Float_004_F.java,,false
Base_Map_004_F.java,,false
Base_Map_002_F.java,,false
Base_Map_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/map/Base_Map_003_T.java,true
Base_Map_001_T.java,,false
Base_StringArray_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_StringArray_001_T.java,true
Base_String_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_String_001_T.java,true
Base_StringBuilder_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_StringBuilder_001_T.java,true
Base_StringBuffer_002_F.java,,false
Base_String_002_F.java,,false
Base_StringBuilder_002_F.java,,false
Base_StringBuffer_001_T.java,,false
Base_StringArray_002_F.java,,false
Base_Queue_004_F.java,,false
Base_Set_004_F.java,,false
Base_List_002_F.java,,false
Base_Set_006_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Set_006_F.java,true
Base_Queue_006_F.java,,false
Base_Set_002_F.java,,false
Base_List_006_F.java,,false
Base_Queue_002_F.java,,false
Base_List_008_F.java,,false
Base_List_004_F.java,,false
Base_List_005_T.java,,false
Base_Set_001_T.java,,false
Base_Queue_001_T.java,,false
Base_Queue_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Queue_003_T.java,true
Base_List_007_T.java,,false
Base_Set_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Set_003_T.java,true
Base_List_003_T.java,,false
Base_Set_005_T.java,,false
Base_List_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_List_001_T.java,true
Base_Queue_005_T.java,,false
Statement_AssertStatement_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/assert_statement/Statement_AssertStatement_002_F.java,true
Statement_AssertStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/assert_statement/Statement_AssertStatement_001_T.java,true
Statement_TryCatchStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/exception_throw/Statement_TryCatchStatement_001_T.java,true
Statement_FinallyStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/exception_throw/Statement_FinallyStatement_001_T.java,true
Statement_TryStatement_002_F.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_002_F.java,true
Statement_TryStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_001_T.java,true
Statement_FinallyStatement_002_F.java,,false
Statement_TryCatchStatement_002_F.java,,false
static_variable_002_F.java,,false
static_variable_001_T.java,,false
Statement_WhileStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_WhileStatement_001_T.java,true
Statement_ForStatement_002_F.java,,false
Statement_DoStatement_002_F.java,,false
Statement_ForEachStatement_002_F.java,,false
Statement_ForStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_ForStatement_001_T.java,true
Statement_ForEachStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_ForEachStatement_001_T.java,true
Statement_DoStatement_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_DoStatement_001_T.java,true
Statement_WhileStatement_002_F.java,,false
Statement_IfStatement_005_T.java,,false
Statement_IfStatement_007_T.java,,false
Statement_IfStatement_003_T.java,,false
Statement_IfStatement_001_T.java,,false
Statement_SwitchStatement_002_F.java,,false
Statement_SwitchStatement_001_T.java,,false
Statement_IfStatement_002_F.java,,false
Statement_IfStatement_006_F.java,,false
Statement_IfStatement_004_F.java,,false
Statement_IfStatement_008_F.java,,false
Expression_CastExpression_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/type_cast/Expression_CastExpression_001_T.java,true
Expression_CastExpression_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/type_cast/Expression_CastExpression_003_T.java,true
Expression_CastExpression_002_F.java,,false
Expression_CastExpression_004_F.java,,false
Expression_LambdaExpression_002_F.java,,false
Expression_LambdaExpression_001_T.java,,false
Expression_ThisExpression_Anonymous_002_F.java,,false
Expression_ThisExpression_002_F.java,,false
Expression_ThisExpression_Lambda_002_F.java,,false
Expression_ThisExpression_001_T.java,,false
Expression_ThisExpression_Lambda_001_T.java,,false
Expression_ThisExpression_Anonymous_001_T.java,,false
Expression_TernaryOperator_001_T.java,,false
Expression_TernaryOperator_002_F.java,,false
Expression_PrefixExpression_001_T.java,,false
Expression_BitOperation_001_T.java,,false
Expression_InstanceofExpression_002_F.java,,false
Statement_VariableDeclarationStatement_001_T.java,,false
Expression_AssignmentExpression_003_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_AssignmentExpression_003_T.java,true
Expression_InfixExpression_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_InfixExpression_001_T.java,true
Expression_PostfixExpression_002_F.java,,false
Expression_ClassInstance_Infix_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_ClassInstance_Infix_001_T.java,true
Expression_AssignmentExpression_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_AssignmentExpression_001_T.java,true
Expression_PostfixExpression_001_T.java,,false
Expression_InfixExpression_002_F.java,,false
Expression_AssignmentExpression_002_F.java,,false
Expression_ClassInstance_Infix_002_F.java,,false
Expression_InstanceofExpression_001_T.java,sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_InstanceofExpression_001_T.java,true
Expression_PrefixExpression_002_F.java,,false
Expression_BitOperation_002_F.java,,false
Expression_AssignmentExpression_004_F.java,,false
Statement_VariableDeclarationStatement_002_F.java,,false
Expression_Reflection_002_F.java,,false
Expression_Reflection_001_T.java,,false
