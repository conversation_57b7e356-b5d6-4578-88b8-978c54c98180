AccessPathChainAlias_004_F.java
AccessPathChainAlias_002_F.java
PropertyIsTaintOrNot_Object_002_F.java
AccessPathChainAlias_001_T.java
PropertyIsTaintOrNot_Object_001_T.java
AccessPathChainAlias_003_T.java
MapPutGet_003_T.java
Array_index_003_T.java
MapPutGet_004_F.java
Array_index_004_F.java
ArrayOutOfBoundOrNot_004_F.java
ArrayOutOfBoundOrNot_002_F.java
Array_index_001_T.java
ArrayOutOfBoundOrNot_003_T.java
Array_index_002_F.java
ArrayOutOfBoundOrNot_001_T.java
MapField_005_T.java
MapField_007_T.java
MapPutGet_001_T.java
MapField_003_T.java
MapField_001_T.java
QueueWithLambda_001_T.java
QueueWithLambda_002_F.java
MapField_002_F.java
MapField_006_F.java
MapField_008_F.java
MapField_004_F.java
MapPutGet_002_F.java
ObjectDiffAttribute_002_F.java
ObjectDiffAttribute_001_T.java
Map_obj_sensitive_005_T.java
Map_obj_sensitive_003_T.java
Map_obj_sensitive_001_T.java
Map_obj_sensitive_002_F.java
Map_obj_sensitive_006_F.java
Map_obj_sensitive_004_F.java
ArrayElementOverwrite_002_F.java
ArrayElementOverwrite_004_F.java
ArrayElementOverwrite_003_T.java
ArrayElementOverwrite_001_T.java
DifferentParamsForFunction_002_F.java
DifferentParamsForFunction_006_F.java
MultiCallSite_002_F.java
DifferentParamsForFunction_004_F.java
DifferentParamsForFunction_005_T.java
MultiCallSite_001_T.java
DifferentParamsForFunction_003_T.java
DifferentParamsForFunction_001_T.java
Expression_Polymorphism_002_F.java
Expression_Polymorphism_001_T.java
return_value_passing_002_F.java
return_value_passing_001_T.java
Expression_MethodInvocation_InfixExpression_002_F.java
DifferentParamsForFunction_006_F.java
Expression_CallExpression_Package_001_T.java
Expression_MethodInvocation_001_T.java
DifferentParamsForFunction_005_T.java
Expression_MethodInvocation_InfixExpression_001_T.java
Expression_MethodInvocation_002_F.java
Expression_CallExpression_Package_002_F.java
Statement_InterruptStatement_002_F.java
Statement_InterruptStatement_006_F.java
Statement_InterruptStatement_008_F.java
Statement_InterruptStatement_004_F.java
Statement_InterruptStatement_005_T.java
Statement_InterruptStatement_007_T.java
Statement_InterruptStatement_003_T.java
Statement_InterruptStatement_001_T.java
ConstantIfGuard_001_T.java
Statement_ForStatement_004_F.java
Statement_ForStatement_003_T.java
ConstantIfGuard_002_F.java
Statement_AssertStatement_006_F.java
Statement_AssertStatement_004_F.java
Statement_WhileStatement_003_T.java
DifferentIfBranch_ArrayLength_002_F.java
Expression_InstanceofExpression_004_F.java
Expression_InstanceofExpression_006_F.java
Statement_WhileStatement_004_F.java
Statement_AssertStatement_003_T.java
Expression_InstanceofExpression_005_T.java
Statement_AssertStatement_005_T.java
DifferentIfBranch_ArrayLength_003_T.java
Expression_InstanceofExpression_003_T.java
DifferentIfBranch_ArrayLength_001_T.java
Statement_TryStatement_006_F.java
Statement_TryStatement_004_F.java
Statement_TryStatement_002_F.java
Statement_TryStatement_003_T.java
Statement_TryStatement_001_T.java
Statement_TryStatement_005_T.java
CompletableFuture_002_F.java
Async_Multithreading_003_T.java
Async_Multithreading_001_T.java
Async_Multithreading_009_T.java
Async_Multithreading_005_T.java
Async_Multithreading_007_T.java
Async_Multithreading_006_F.java
Async_Multithreading_008_F.java
Async_Multithreading_004_F.java
CompletableFuture_001_T.java
Async_Multithreading_010_F.java
Async_Multithreading_002_F.java
FlowSensitiveAlias_001_T.java
AssignedByVariable_002_T.java
AssignObjectAttribute_001_F.java
AssignedByFixedValue_001_F.java
FlowSensitiveAlias_003_T.java
FlowSensitiveAlias_004_F.java
AssignedByVariable_001_F.java
FlowSensitiveAlias_002_F.java
AssignObjectAttribute_002_T.java
AssignedByFixedValue_002_T.java
Statement_ForStatement_008_F.java
Statement_ForStatement_006_F.java
Statement_ForStatement_007_T.java
Statement_ForStatement_005_T.java
simple_object_002_F.java
simple_object_001_T.java
CallExpression_CustomCode_Class_007_T.java
CallExpression_CustomCode_Class_008_F.java
CallExpression_CustomCode_Class_005_T.java
CallExpression_CustomCode_Class_006_F.java
CallExpression_CustomCode_Class_001_T.java
CallExpression_CustomCode_Class_003_T.java
CallExpression_CustomCode_Interface_003_T.java
CallExpression_CustomCode_Class_004_F.java
CallExpression_CustomCode_Interface_004_F.java
CallExpression_CustomCode_Class_002_F.java
ReturnAlias_001_T.java
FieldUnAlias_002_T.java
InnerClassAlias_006_T.java
InnerClassAlias_002_F.java
SingleFieldAccessAlias_002_F.java
InnerClassAlias_010_F.java
StaticFieldAlias_001_T.java
HeapOverwriteAlias_002_F.java
ReturnAlias_003_T.java
FieldUnAlias_004_F.java
NullAlias_002_F.java
InnerClassAlias_004_F.java
InnerClassAlias_008_F.java
HeapPointsToSelfAlias_001_T.java
PrimitiveFieldAccess_001_T.java
PrimitiveFieldAccess_003_T.java
HeapOverwriteAlias_004_F.java
SameArgumentAlias_002_F.java
InnerClassAlias_003_F.java
InnerClassAlias_007_T.java
ReturnAlias_004_F.java
FieldUnAlias_003_T.java
HeapPointsToSelfAlias_002_F.java
PrimitiveFieldAccess_002_F.java
SameArgumentAlias_001_T.java
InnerClassAlias_005_T.java
InnerClassAlias_009_T.java
FieldUnAlias_001_F.java
PrimitiveFieldAccess_004_F.java
ReturnAlias_002_F.java
HeapOverwriteAlias_003_T.java
InnerClassAlias_001_T.java
SingleFieldAccessAlias_001_T.java
StaticFieldAlias_002_F.java
HeapOverwriteAlias_001_T.java
NullAlias_001_T.java
CompletableFuture_002_F.java
CompletableFuture_004_F.java
CompletableFuture_001_T.java
CompletableFuture_003_T.java
Async_Multithreading_003_T.java
Async_Multithreading_001_T.java
Async_Multithreading_009_T.java
Async_Multithreading_005_T.java
Async_Multithreading_007_T.java
Async_Multithreading_006_F.java
Async_Multithreading_008_F.java
Async_Multithreading_004_F.java
Async_Multithreading_010_F.java
Async_Multithreading_002_F.java
cross_file_001_T_a.java
cross_file_001_T_b.java
cross_file_002_F_b.java
cross_file_002_F_a.java
cross_directory_002_F_a.java
cross_directory_002_F_b.java
cross_directory_001_T_a.java
cross_directory_001_T_b.java
Expression_MethodInvocation_Argument_001_F.java
Expression_MethodInvocation_Argument_002_T.java
Expression_Polymorphism_002_F.java
Expression_Polymorphism_001_T.java
static_method_002_F.java
static_field_002_F.java
static_field_001_T.java
static_method_001_T.java
return_value_passing_002_F.java
return_value_passing_001_T.java
Expression_MethodInvocation_InfixExpression_002_F.java
Expression_CallExpression_Package_001_T.java
Expression_MethodInvocation_001_T.java
Expression_MethodInvocation_InfixExpression_001_T.java
Expression_MethodInvocation_002_F.java
Expression_CallExpression_Package_002_F.java
higher_order_function_001_T.java
higher_order_function_002_F.java
CallExpression_NoSourceCode_Native_003_F.java
CallExpression_CustomCode_Interface_001_T.java
CallExpression_NoSourceCode_Native_001_F.java
Expression_CallExpression_Array_002_T.java
Expression_MethodInvocation_MethodInvocation_002_T.java
CallExpression_NoSourceCode_Native_005_F.java
CallExpression_NoSourceCode_Native_006_T.java
CallExpression_NoSourceCode_Native_004_T.java
CallExpression_CustomCode_Interface_002_F.java
Expression_CallExpression_Array_001_F.java
Expression_MethodInvocation_MethodInvocation_001_F.java
CallExpression_NoSourceCode_Native_002_T.java
CallExpression_CustomCode_Class_011_T.java
CallExpression_CustomCode_Class_009_T.java
CallExpression_CustomCode_Class_010_F.java
CallExpression_CustomCode_Class_012_F.java
Base_ArrayAccess_006_F.java
Base_ArrayAccess_008_F.java
Base_ArrayAccess_004_F.java
Base_ArrayAccess_010_F.java
Base_ArrayAccess_002_F.java
Base_ArrayAccess_003_T.java
Base_ArrayAccess_001_T.java
Base_ArrayAccess_009_T.java
Base_ArrayAccess_005_T.java
Base_ArrayAccess_007_T.java
Base_Byte_006_F.java
Base_Char_001_T.java
Base_Long_001_T.java
Base_Double_004_F.java
Base_Byte_008_F.java
Base_Long_003_T.java
Base_Char_003_T.java
Base_Byte_004_F.java
Base_ByteArray_004_F.java
Base_Integer_002_F.java
Base_Double_002_F.java
Base_Char_007_T.java
Base_Long_007_T.java
Base_Float_003_T.java
Base_CharArray_001_T.java
Base_Integer_006_F.java
Base_Integer_004_F.java
Base_CharArray_003_T.java
Base_Float_001_T.java
Base_Integer_008_F.java
Base_ByteArray_002_F.java
Base_Long_005_T.java
Base_Byte_002_F.java
Base_Char_005_T.java
Base_Long_004_F.java
Base_Char_008_F.java
Base_Byte_003_T.java
Base_Char_004_F.java
Base_Long_008_F.java
Base_Double_001_T.java
Base_ByteArray_003_T.java
Base_CharArray_002_F.java
Base_Integer_005_T.java
Base_Float_002_F.java
Base_Integer_007_T.java
Base_Char_006_F.java
Base_Byte_001_T.java
Base_Long_006_F.java
Base_Double_003_T.java
Base_ByteArray_001_T.java
Base_Integer_003_T.java
Base_CharArray_004_F.java
Base_Long_002_F.java
Base_Char_002_F.java
Base_Byte_005_T.java
Base_Byte_007_T.java
Base_Integer_001_T.java
Base_Float_004_F.java
Base_Map_004_F.java
Base_Map_002_F.java
Base_Map_003_T.java
Base_Map_001_T.java
Base_StringArray_001_T.java
Base_String_001_T.java
Base_StringBuilder_001_T.java
Base_StringBuffer_002_F.java
Base_String_002_F.java
Base_StringBuilder_002_F.java
Base_StringBuffer_001_T.java
Base_StringArray_002_F.java
Base_Queue_004_F.java
Base_Set_004_F.java
Base_List_002_F.java
Base_Set_006_F.java
Base_Queue_006_F.java
Base_Set_002_F.java
Base_List_006_F.java
Base_Queue_002_F.java
Base_List_008_F.java
Base_List_004_F.java
Base_List_005_T.java
Base_Set_001_T.java
Base_Queue_001_T.java
Base_Queue_003_T.java
Base_List_007_T.java
Base_Set_003_T.java
Base_List_003_T.java
Base_Set_005_T.java
Base_List_001_T.java
Base_Queue_005_T.java
Statement_AssertStatement_002_F.java
Statement_AssertStatement_001_T.java
Statement_TryCatchStatement_001_T.java
Statement_FinallyStatement_001_T.java
Statement_TryStatement_002_F.java
Statement_TryStatement_001_T.java
Statement_FinallyStatement_002_F.java
Statement_TryCatchStatement_002_F.java
static_variable_002_F.java
static_variable_001_T.java
Statement_WhileStatement_001_T.java
Statement_ForStatement_002_F.java
Statement_DoStatement_002_F.java
Statement_ForEachStatement_002_F.java
Statement_ForStatement_001_T.java
Statement_ForEachStatement_001_T.java
Statement_DoStatement_001_T.java
Statement_WhileStatement_002_F.java
Statement_IfStatement_005_T.java
Statement_IfStatement_007_T.java
Statement_IfStatement_003_T.java
Statement_IfStatement_001_T.java
Statement_SwitchStatement_002_F.java
Statement_SwitchStatement_001_T.java
Statement_IfStatement_002_F.java
Statement_IfStatement_006_F.java
Statement_IfStatement_004_F.java
Statement_IfStatement_008_F.java
Expression_CastExpression_001_T.java
Expression_CastExpression_003_T.java
Expression_CastExpression_002_F.java
Expression_CastExpression_004_F.java
Expression_LambdaExpression_002_F.java
Expression_LambdaExpression_001_T.java
Expression_ThisExpression_Anonymous_002_F.java
Expression_ThisExpression_002_F.java
Expression_ThisExpression_Lambda_002_F.java
Expression_ThisExpression_001_T.java
Expression_ThisExpression_Lambda_001_T.java
Expression_ThisExpression_Anonymous_001_T.java
Expression_TernaryOperator_001_T.java
Expression_TernaryOperator_002_F.java
Expression_PrefixExpression_001_T.java
Expression_BitOperation_001_T.java
Expression_InstanceofExpression_002_F.java
Statement_VariableDeclarationStatement_001_T.java
Expression_AssignmentExpression_003_T.java
Expression_InfixExpression_001_T.java
Expression_PostfixExpression_002_F.java
Expression_ClassInstance_Infix_001_T.java
Expression_AssignmentExpression_001_T.java
Expression_PostfixExpression_001_T.java
Expression_InfixExpression_002_F.java
Expression_AssignmentExpression_002_F.java
Expression_ClassInstance_Infix_002_F.java
Expression_InstanceofExpression_001_T.java
Expression_PrefixExpression_002_F.java
Expression_BitOperation_002_F.java
Expression_AssignmentExpression_004_F.java
Statement_VariableDeclarationStatement_002_F.java
Expression_Reflection_002_F.java
Expression_Reflection_001_T.java