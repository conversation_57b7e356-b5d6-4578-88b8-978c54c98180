#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP地理位置分析脚本
读取ip.csv文件，为每个IP地址查询地理位置信息，并生成包含位置信息的新CSV文件
"""

import csv
import urllib.request
import urllib.parse
import time
import json
from typing import Dict, Optional
import sys

class IPLocationAnalyzer:
    def __init__(self):
        # 使用免费的IP地理位置查询API
        self.api_urls = [
            "http://ip-api.com/json/{ip}?lang=zh-CN",  # 免费，支持中文
            "https://ipapi.co/{ip}/json/",  # 备用API
        ]
        self.delay = 1.5  # API调用间隔，避免频率限制
        
    def get_ip_location(self, ip: str) -> Dict[str, str]:
        """
        查询IP地址的地理位置信息
        
        Args:
            ip: IP地址字符串
            
        Returns:
            包含位置信息的字典
        """
        location_info = {
            'country': '未知',
            'region': '未知', 
            'city': '未知',
            'isp': '未知',
            'full_location': '未知'
        }
        
        # 尝试第一个API (ip-api.com)
        try:
            url = self.api_urls[0].format(ip=ip)
            with urllib.request.urlopen(url, timeout=10) as response:
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                
                    if data.get('status') == 'success':
                        location_info['country'] = data.get('country', '未知')
                        location_info['region'] = data.get('regionName', '未知')
                        location_info['city'] = data.get('city', '未知')
                        location_info['isp'] = data.get('isp', '未知')

                        # 组合完整位置信息
                        parts = []
                        if location_info['country'] != '未知':
                            parts.append(location_info['country'])
                        if location_info['region'] != '未知' and location_info['region'] != location_info['country']:
                            parts.append(location_info['region'])
                        if location_info['city'] != '未知' and location_info['city'] != location_info['region']:
                            parts.append(location_info['city'])

                        location_info['full_location'] = ' - '.join(parts) if parts else '未知'

                        print(f"✓ {ip}: {location_info['full_location']}")
                        return location_info
                    
        except Exception as e:
            print(f"⚠ API调用失败 {ip}: {str(e)}")
        
        # 如果第一个API失败，尝试备用API
        try:
            url = self.api_urls[1].format(ip=ip)
            with urllib.request.urlopen(url, timeout=10) as response:
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                
                    if 'error' not in data:
                        location_info['country'] = data.get('country_name', '未知')
                        location_info['region'] = data.get('region', '未知')
                        location_info['city'] = data.get('city', '未知')
                        location_info['isp'] = data.get('org', '未知')

                        # 组合完整位置信息
                        parts = []
                        if location_info['country'] != '未知':
                            parts.append(location_info['country'])
                        if location_info['region'] != '未知':
                            parts.append(location_info['region'])
                        if location_info['city'] != '未知':
                            parts.append(location_info['city'])

                        location_info['full_location'] = ' - '.join(parts) if parts else '未知'

                        print(f"✓ {ip}: {location_info['full_location']}")
                        return location_info
                    
        except Exception as e:
            print(f"⚠ 备用API调用失败 {ip}: {str(e)}")
        
        print(f"✗ 无法获取位置信息: {ip}")
        return location_info
    
    def process_csv(self, input_file: str = 'ip.csv', output_file: str = 'ip_with_location.csv'):
        """
        处理CSV文件，为每个IP添加地理位置信息
        
        Args:
            input_file: 输入CSV文件路径
            output_file: 输出CSV文件路径
        """
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                reader = csv.DictReader(infile)
                
                # 准备输出数据
                output_data = []
                total_ips = 0
                processed_ips = 0
                
                print("开始处理IP地址...")
                print("-" * 50)
                
                for row in reader:
                    ip = row.get('ip', '').strip()
                    count = row.get('数量', '')
                    
                    if not ip:  # 跳过空行
                        continue
                        
                    total_ips += 1
                    
                    # 获取地理位置信息
                    location_info = self.get_ip_location(ip)
                    
                    # 组合输出数据
                    output_row = {
                        'ip': ip,
                        '数量': count,
                        '国家': location_info['country'],
                        '省份/地区': location_info['region'],
                        '城市': location_info['city'],
                        'ISP': location_info['isp'],
                        '完整位置': location_info['full_location']
                    }
                    
                    output_data.append(output_row)
                    processed_ips += 1
                    
                    # 添加延迟避免API限制
                    time.sleep(self.delay)
                
                # 写入输出文件
                with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
                    if output_data:
                        fieldnames = ['ip', '数量', '国家', '省份/地区', '城市', 'ISP', '完整位置']
                        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(output_data)
                
                print("-" * 50)
                print(f"处理完成！")
                print(f"总IP数量: {total_ips}")
                print(f"成功处理: {processed_ips}")
                print(f"输出文件: {output_file}")
                
        except FileNotFoundError:
            print(f"错误: 找不到输入文件 {input_file}")
            sys.exit(1)
        except Exception as e:
            print(f"处理过程中发生错误: {str(e)}")
            sys.exit(1)

def main():
    """主函数"""
    print("IP地理位置分析工具")
    print("=" * 50)
    
    analyzer = IPLocationAnalyzer()
    analyzer.process_csv()

if __name__ == "__main__":
    main()
