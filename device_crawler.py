import requests
import time
import json

# 配置常量
API_URL = "https://tmapi.tjnanhong.com/api/device/near_v2"
HEADERS = {
    "Host": "tmapi.tjnanhong.com",
    "Os": "applet",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJqd3RfeWgiLCJleHAiOjE3NDA2Mzk4MzYsInN1YiI6IllIIiwiYXVkIjoiZXZlcnkiLCJuYmYiOjE3NDAwMzUwMzYsImlhdCI6MTc0MDAzNTAzNiwianRpIjoxMDAwMSwidWlkIjoyMTY3NTd9.I8ait-rNbuDa31aD3CyF_zuLPGSb4oU-mjMfpJId3bY",
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.56(0x18003831) NetType/WIFI Language/zh_CN"
}

def fetch_all_devices():
    page = 1
    all_devices = []
    
    while True:
        try:
            response = requests.post(
                API_URL,
                headers=HEADERS,
                json={"page": page},
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"Page {page} 请求失败，状态码：{response.status_code}")
                break
                
            data = response.json()
            if not data.get('success'):
                print(f"Page {page} API返回错误：{data.get('msg')}")
                break
            
            devices = data.get('data', {}).get('nearList', [])
            if not devices:
                print("已获取所有数据")
                break
            
            all_devices.extend(devices)  # 累积全部数据
            print(f"Page {page} 完成，获取到 {len(devices)} 条数据")
            page += 1
            
            time.sleep(1)
            
        except requests.exceptions.RequestException as e:
            print(f"请求异常：{str(e)}")
            time.sleep(5)
        except Exception as e:
            print(f"处理异常：{str(e)}")
            break
    
    # 全部完成后写入JSON文件
    with open('devices.json', 'w', encoding='utf-8') as f:
        json.dump(all_devices, f, ensure_ascii=False, indent=2)
    
    return all_devices

if __name__ == "__main__":
    fetch_all_devices()
    print("数据已保存到 devices.json") 