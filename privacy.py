import requests
from bs4 import BeautifulSoup
import re
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def fetch_webpage_with_selenium(url):
    """Fetch webpage content using Selenium to render JavaScript."""
    print("初始化 Selenium...")
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    
    print(f"访问网页: {url}")
    driver.get(url)
    # 等待JavaScript加载
    time.sleep(5)
    
    html_content = driver.page_source
    driver.quit()
    return html_content

def extract_tables_or_divs(html_content):
    """Extract tables or div-based table-like structures from the HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 尝试查找标准表格
    tables = soup.find_all('table')
    if tables:
        print(f"找到 {len(tables)} 个标准表格。")
        return tables, "table"
    
    # 尝试查找表格样式的div结构
    # 这里假设表格可能是使用div.table或有特定类名的div来实现的
    div_tables = soup.find_all('div', class_=lambda c: c and ('table' in c.lower() or 'grid' in c.lower()))
    if div_tables:
        print(f"找到 {len(div_tables)} 个DIV表格结构。")
        return div_tables, "div"
    
    # 尝试查找整个文章内容，我们将提取并格式化所有内容
    article = soup.find('div', class_='article')
    if article:
        print("找到文章内容，将提取所有文本。")
        return [article], "article"
    
    return [], ""

def extract_table_structure(element, element_type):
    """从元素中提取表格结构或文本内容"""
    if element_type == "table":
        # 处理标准HTML表格
        return table_to_markdown(element)
    elif element_type == "div":
        # 处理DIV表格
        return div_table_to_markdown(element)
    elif element_type == "article":
        # 处理文章内容
        return article_to_markdown(element)
    return ""

def table_to_markdown(table):
    """将HTML表格转换为markdown格式"""
    rows = table.find_all('tr')
    if not rows:
        return "空表格或无效表格结构。"
    
    markdown_table = []
    
    # 处理表头行
    header_cells = rows[0].find_all(['th', 'td'])
    header = [cell.get_text(strip=True) for cell in header_cells]
    markdown_table.append('| ' + ' | '.join(header) + ' |')
    
    # 添加分隔行
    markdown_table.append('| ' + ' | '.join(['---'] * len(header)) + ' |')
    
    # 处理数据行
    for row in rows[1:]:
        cells = row.find_all('td')
        row_data = [cell.get_text(strip=True) for cell in cells]
        markdown_table.append('| ' + ' | '.join(row_data) + ' |')
    
    return '\n'.join(markdown_table)

def div_table_to_markdown(div_table):
    """将DIV表格转换为markdown格式"""
    # 这需要根据实际页面结构调整
    rows = div_table.find_all('div', recursive=False)
    if not rows:
        return "未找到行结构。"
    
    markdown_table = []
    header_row = rows[0]
    header_cells = header_row.find_all('div', recursive=False)
    
    if not header_cells:
        # 尝试查找其他可能的表格结构
        return "复杂的DIV表格结构，直接提取文本内容：\n\n" + div_table.get_text(strip=True)
    
    header = [cell.get_text(strip=True) for cell in header_cells]
    markdown_table.append('| ' + ' | '.join(header) + ' |')
    markdown_table.append('| ' + ' | '.join(['---'] * len(header)) + ' |')
    
    for row in rows[1:]:
        cells = row.find_all('div', recursive=False)
        row_data = [cell.get_text(strip=True) for cell in cells]
        markdown_table.append('| ' + ' | '.join(row_data) + ' |')
    
    return '\n'.join(markdown_table)

def article_to_markdown(article):
    """将文章内容提取为markdown格式，尝试识别表格样式的部分"""
    # 提取所有文本，特别注意寻找表格式样的段落
    all_paragraphs = article.find_all(['p', 'div', 'section'])
    
    markdown_content = []
    in_table_section = False
    current_table = []
    
    for p in all_paragraphs:
        text = p.get_text(strip=True)
        
        # 判断是否可能是表格的一部分（包含"："或结构化的内容）
        if "：" in text or ":" in text:
            # 可能是表格的键值对
            parts = re.split(r'[：:]', text, 1)
            if len(parts) == 2:
                if not in_table_section:
                    in_table_section = True
                    current_table = []
                    current_table.append('| 项目 | 内容 |')
                    current_table.append('| --- | --- |')
                
                current_table.append(f'| {parts[0].strip()} | {parts[1].strip()} |')
                continue
        
        # 如果有表格内容且当前不是表格的一部分，将表格添加到内容中
        if in_table_section and text and not ("：" in text or ":" in text):
            in_table_section = False
            if current_table:
                markdown_content.append('\n'.join(current_table))
                markdown_content.append("")  # 空行分隔
                current_table = []
        
        # 添加普通段落文本
        if text and not in_table_section:
            markdown_content.append(text)
            markdown_content.append("")  # 空行分隔
    
    # 如果结束时还有表格内容，添加它
    if in_table_section and current_table:
        markdown_content.append('\n'.join(current_table))
    
    return '\n'.join(markdown_content)

def main():
    # 隐私政策页面的URL
    url = "https://m.hellobike.com/ebike-h5/latest/article.html?guid=4656dfaa937e41d0b2057fc6326372cf"
    output_file = "privacy_tables.md"
    
    # 使用Selenium获取完整的页面内容
    html_content = fetch_webpage_with_selenium(url)
    
    print("提取表格或类表格结构...")
    elements, element_type = extract_tables_or_divs(html_content)
    
    if not elements:
        print("在网页上未找到表格或可识别的内容结构。")
        
        # 保存原始HTML以便检查
        with open("debug_page_content.html", 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("已保存原始HTML内容到debug_page_content.html以便检查。")
        return
    
    print(f"找到 {len(elements)} 个可提取的内容元素。转换为markdown...")
    
    # 转换所有表格为markdown并保存到单个文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# 隐私政策内容\n\n")
        f.write(f"来源: [哈啰出行隐私政策]({url})\n\n")
        
        for i, element in enumerate(elements):
            f.write(f"## 部分 {i+1}\n\n")
            content = extract_table_structure(element, element_type)
            f.write(content + "\n\n")
    
    print(f"所有内容已保存到 {output_file}")

if __name__ == "__main__":
    main() 