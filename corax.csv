文件路径
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/complex_object/CallExpression_CustomCode_Class_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/exception_throw/Statement_FinallyStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/common/utils/CmdUtil.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/assert_statement/Statement_AssertStatement_002_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapOverwriteAlias_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/set_array/ArrayElementOverwrite_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_String_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/FlowSensitiveAlias_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Set_006_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_List_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_StringBuilder_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_WhileStatement_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_AssignmentExpression_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/array/Array_index_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/FieldUnAlias_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_AssignmentExpression_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_WhileStatement_004_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignedByVariable_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/asynchronous/Async_Multithreading_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/asynchronous_tracing/multi_thread/Async_Multithreading_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/exception_throw/Statement_TryStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/ReturnAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_009_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Expression_InstanceofExpression_004_F.java
sast-java/src/main/java/com/sast/astbenchmark/model/custom/TT.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/map/Base_Map_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/PropertyIsTaintOrNot_Object_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/complex_object/CallExpression_CustomCode_Class_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_DoStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/exception_throw/Statement_TryStatement_002_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/Expression_MethodInvocation_MethodInvocation_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/SameArgumentAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/model/custom/T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_006_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_002_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/StaticFieldAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapOverwriteAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/no_solver/Statement_ForStatement_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_ForEachStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/generics/CallExpression_CustomCode_Class_011_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/no_solver/ConstantIfGuard_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_008_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/cross_file_package_namespace/cross_directory/cross_directory_001_T/B/cross_directory_001_T_b.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Queue_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_004_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_007_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_InfixExpression_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/DifferentIfBranch_ArrayLength_002_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_ForStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/DifferentParamsForFunction_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/HeapPointsToSelfAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_NoSourceCode_Native_004_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/polymorphism/Expression_Polymorphism_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/override/Expression_Polymorphism_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/AccessPathChainAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/loop_stmt/Statement_ForStatement_008_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Expression_InstanceofExpression_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/return_value_passing/return_value_passing_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/return_value_passing/return_value_passing_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/loop_conditional_stmt/solver/Statement_AssertStatement_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_009_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_004_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/collections/Base_Set_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/ReturnAlias_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/static_method/static_method_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/exception_throw/Statement_TryCatchStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/interface_class/ObjectDiffAttribute_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_006_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_006_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/argument_return_value_passing/argument_value_passing/DifferentParamsForFunction_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/DifferentParamsForFunction_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/array/ArrayOutOfBoundOrNot_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/FlowSensitiveAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/anonymous_object/CallExpression_CustomCode_Class_007_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_ClassInstance_Infix_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/basic_expression_operation/Expression_InstanceofExpression_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/array/Base_ArrayAccess_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/NullAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/FieldUnAlias_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/object_sensitive/set_array/ArrayElementOverwrite_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignObjectAttribute_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/generics/CallExpression_CustomCode_Class_009_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_muilt_collection/Array_index_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/datatype/string/Base_StringArray_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_interface_class/AccessPathChainAlias_003_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/object_field_sensitive/field_sensitive_one_collection/numeric_index_state_no_solver/map/MapField_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/MultiCallSite_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/path_sensitive/explicit_jump_control/Statement_InterruptStatement_007_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/InnerClassAlias_007_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/context_sensitive/multi_invoke/DifferentParamsForFunction_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/control_flow/loop_stmt/Statement_WhileStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/interface_class/abstract_class/CallExpression_CustomCode_Class_005_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/cross_file_package_namespace/cross_file/cross_file_001_T/cross_file_001_T_b.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/alias/SingleFieldAccessAlias_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/exception_error/assert_statement/Statement_AssertStatement_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/accuracy/flow_sensitive/normal_stmt/AssignedByFixedValue_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/type_cast/Expression_CastExpression_001_T.java
sast-java/src/main/java/com/sast/astbenchmark/common/utils/JDBCUtil.java
sast-java/src/main/java/com/sast/astbenchmark/service/SSRFShowManageImpl.java
sast-java/src/main/java/com/sast/astbenchmark/service/SSRFShowManageImpl.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/chained_call/Expression_MethodInvocation_Argument_002_T.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/expression/type_cast/Expression_CastExpression_003_T.java
source/empty/org/apache/http/impl/execchain/ProtocolExec.java
source/empty/org/apache/http/impl/execchain/ProtocolExec.java
source/empty/org/apache/http/client/methods/HttpGet.java
sast-java/src/main/java/com/sast/astbenchmark/service/DefaultSsrfClient.java
source/empty/cn/hutool/http/HttpRequest.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_CustomCode_Interface_002_F.java
sast-java/src/main/java/com/sast/astbenchmark/case_language_maturity/completeness/single_app_tracing/function_call/library_function/CallExpression_CustomCode_Interface_001_T.java