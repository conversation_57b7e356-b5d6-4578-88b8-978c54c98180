import requests
from bs4 import BeautifulSoup
import argparse
import sys
from datetime import datetime
import time
import random
import re

def get_iplark_data(ip_address):
    """从iplark.com获取IP信息"""
    url = f"https://iplark.com/{ip_address}"
    
    # 设置随机User-Agent以模拟不同浏览器，避免被检测为爬虫
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    ]
    
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://iplark.com/'
    }
    
    # 初始化结果字典
    result = {
        'ip': ip_address,
        'query_url': url,
        'success': False
    }
    
    try:
        # 添加随机延迟，避免频繁请求
        time.sleep(random.uniform(0.5, 1.5))
        
        # 发送请求
        print(f"正在从iplark.com查询IP: {ip_address} 的信息...")
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # 获取页面内容
        html_content = response.text
        
        # 使用BeautifulSoup解析HTML内容
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 设置成功标志
        result['success'] = True
        
        # 调试信息 - 查看页面是否有内容
        page_title = soup.title.text if soup.title else "无标题"
        print(f"页面标题: {page_title}")
        
        # 提取所有可能的信息
        
        # 方法1: 使用多种选择器尝试提取信息
        result.update(extract_by_selectors(soup))
        
        # 方法2: 查找页面上所有表格
        result.update(extract_from_tables(soup))
        
        # 方法3: 使用正则表达式从页面文本中提取信息
        result.update(extract_by_regex(html_content))
        
        # 提取所有div中可能包含的信息
        result.update(extract_from_divs(soup))
        
        # 如果没有提取到国家信息，尝试从页面中查找国家名称
        if not result.get('country'):
            countries = ['中国', '美国', '日本', '韩国', '俄罗斯', '加拿大', '英国', '法国', '德国', '澳大利亚']
            for country in countries:
                if country in html_content:
                    result['country'] = country
                    break
        
        return result
    
    except requests.exceptions.RequestException as e:
        print(f"iplark.com请求错误: {e}")
        return result
    except Exception as e:
        print(f"解析数据时出错: {e}")
        return result

def extract_by_selectors(soup):
    """尝试使用多种选择器提取信息"""
    result = {}
    
    # 尝试多种可能的选择器
    selectors = {
        'country': ['div.country', '.country-name', '.country-info', 'span.country'],
        'region': ['div.region', '.region-name', '.province', 'span.region', '.state-name'],
        'city': ['div.city', '.city-name', 'span.city'],
        'isp': ['div.isp', '.isp-name', 'span.isp', '.provider'],
        'org': ['div.org', '.org-name', '.organization', 'span.organization'],
        'asn': ['div.asn', '.asn-number', 'span.asn']
    }
    
    # 遍历所有选择器尝试提取信息
    for key, selector_list in selectors.items():
        for selector in selector_list:
            element = soup.select_one(selector)
            if element and element.text.strip():
                result[key] = element.text.strip()
                break
    
    return result

def extract_from_tables(soup):
    """从页面上的表格中提取信息"""
    result = {}
    
    # 查找页面上所有表格
    tables = soup.find_all('table')
    
    for table in tables:
        rows = table.find_all('tr')
        for row in rows:
            # 提取表格中的行
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:
                key = cells[0].text.strip().lower()
                value = cells[1].text.strip()
                
                # 处理常见的键名
                key = key.replace(':', '').replace('：', '')
                key = key.replace(' ', '_').replace('-', '_')
                
                # 匹配常见字段
                if 'country' in key or '国家' in key or '国' in key:
                    result['country'] = value
                elif 'region' in key or 'province' in key or '地区' in key or '省' in key:
                    result['region'] = value
                elif 'city' in key or '城市' in key or '市' in key:
                    result['city'] = value
                elif 'isp' in key or '服务商' in key or '供应商' in key:
                    result['isp'] = value
                elif 'organization' in key or 'org' in key or '组织' in key:
                    result['org'] = value
                elif 'asn' in key or 'as' in key:
                    result['asn'] = value
                elif 'hostname' in key or '主机名' in key:
                    result['hostname'] = value
                elif 'proxy' in key or '代理' in key:
                    result['proxy'] = 'yes' in value.lower() or '是' in value
                elif 'vpn' in key:
                    result['vpn'] = 'yes' in value.lower() or '是' in value
                elif 'tor' in key:
                    result['tor'] = 'yes' in value.lower() or '是' in value
                elif 'latitude' in key or '纬度' in key:
                    result['latitude'] = value
                elif 'longitude' in key or '经度' in key:
                    result['longitude'] = value
                else:
                    # 存储其他信息
                    clean_key = re.sub(r'[^a-z0-9_]', '', key.lower())
                    if clean_key and value:
                        result[clean_key] = value
    
    # 如果有经纬度信息，合并为location
    if 'latitude' in result and 'longitude' in result:
        result['loc'] = f"{result['latitude']}, {result['longitude']}"
    
    return result

def extract_by_regex(html_content):
    """使用正则表达式从HTML内容中提取信息"""
    result = {}
    
    # IP地址模式
    ip_pattern = r'"ip"\s*:\s*"([^"]+)"'
    ip_match = re.search(ip_pattern, html_content)
    if ip_match:
        result['ip_from_json'] = ip_match.group(1)
    
    # 国家模式
    country_pattern = r'"country"\s*:\s*"([^"]+)"'
    country_match = re.search(country_pattern, html_content)
    if country_match and not result.get('country'):
        result['country'] = country_match.group(1)
    
    # 区域模式
    region_pattern = r'"region"\s*:\s*"([^"]+)"'
    region_match = re.search(region_pattern, html_content)
    if region_match and not result.get('region'):
        result['region'] = region_match.group(1)
    
    # 城市模式
    city_pattern = r'"city"\s*:\s*"([^"]+)"'
    city_match = re.search(city_pattern, html_content)
    if city_match and not result.get('city'):
        result['city'] = city_match.group(1)
    
    # ISP模式
    isp_pattern = r'"isp"\s*:\s*"([^"]+)"'
    isp_match = re.search(isp_pattern, html_content)
    if isp_match and not result.get('isp'):
        result['isp'] = isp_match.group(1)
    
    # 组织模式
    org_pattern = r'"org"\s*:\s*"([^"]+)"'
    org_match = re.search(org_pattern, html_content)
    if org_match and not result.get('org'):
        result['org'] = org_match.group(1)
    
    # ASN模式
    asn_pattern = r'"as"\s*:\s*"([^"]+)"'
    asn_match = re.search(asn_pattern, html_content)
    if asn_match and not result.get('asn'):
        result['asn'] = asn_match.group(1)
    
    # 经纬度模式
    loc_pattern = r'"loc"\s*:\s*"([^"]+)"'
    loc_match = re.search(loc_pattern, html_content)
    if loc_match and not result.get('loc'):
        result['loc'] = loc_match.group(1)
    
    return result

def extract_from_divs(soup):
    """从所有div元素中提取信息"""
    result = {}
    
    # 查找所有可能包含信息的div
    divs = soup.find_all('div', class_=True)
    
    # 关键词映射
    keywords = {
        'country': ['country', '国家', '国'],
        'region': ['region', 'province', '地区', '省', '州'],
        'city': ['city', '城市', '市'],
        'isp': ['isp', 'provider', '服务商', '供应商'],
        'org': ['organization', 'org', '组织'],
        'asn': ['asn', 'as'],
        'hostname': ['hostname', 'host', '主机名'],
        'proxy': ['proxy', '代理'],
        'vpn': ['vpn'],
        'tor': ['tor'],
        'location': ['location', 'loc', '位置', '坐标']
    }
    
    for div in divs:
        # 查找div中的所有文本
        div_text = div.text.strip()
        
        # 检查div的class名和文本内容，寻找匹配关键词的内容
        div_class = div.get('class', [])
        div_class_str = ' '.join(div_class).lower()
        
        for result_key, keyword_list in keywords.items():
            # 如果结果已有此项，跳过
            if result_key in result:
                continue
                
            # 检查class名是否包含关键词
            for keyword in keyword_list:
                if keyword.lower() in div_class_str:
                    # 找到匹配的class，现在提取值
                    # 尝试查找该div中的span或其他标签
                    value_element = div.find(['span', 'strong', 'b', 'p'])
                    if value_element:
                        result[result_key] = value_element.text.strip()
                    else:
                        # 提取div的文本，尝试分离标签和值
                        text_parts = re.split(r'[:：\s]\s*', div_text, 1)
                        if len(text_parts) > 1:
                            result[result_key] = text_parts[1].strip()
                    break
            
            # 如果没有通过class找到，尝试通过文本内容找
            if result_key not in result:
                for keyword in keyword_list:
                    pattern = rf'{keyword}[:：\s]\s*([^\n,]+)'
                    match = re.search(pattern, div_text, re.IGNORECASE)
                    if match:
                        result[result_key] = match.group(1).strip()
                        break
    
    return result

def display_ip_info(ip_address):
    """显示从iplark.com获取的IP信息"""
    
    # 获取IP信息
    ip_info = get_iplark_data(ip_address)
    
    if not ip_info.get('success'):
        print(f"无法从iplark.com获取IP: {ip_address} 的信息")
        return
    
    # 打印基本信息
    print("\n===== IP地址信息 (来源: iplark.com) =====")
    print(f"IP地址: {ip_address}")
    print(f"查询链接: {ip_info.get('query_url', '未知')}")
    
    # 是否有位置信息
    has_location = any(key in ip_info for key in ['country', 'region', 'city', 'loc'])
    if has_location:
        print("\n----- 位置信息 -----")
        print(f"国家: {ip_info.get('country', '未知')}")
        print(f"地区/省份: {ip_info.get('region', '未知')}")
        print(f"城市: {ip_info.get('city', '未知')}")
        print(f"位置坐标: {ip_info.get('loc', '未知')}")
        if 'postal' in ip_info:
            print(f"邮政编码: {ip_info.get('postal', '未知')}")
        if 'timezone' in ip_info:
            print(f"时区: {ip_info.get('timezone', '未知')}")
    
    # 是否有网络信息
    has_network = any(key in ip_info for key in ['isp', 'org', 'asn', 'hostname', 'domain'])
    if has_network:
        print("\n----- 网络信息 -----")
        if 'isp' in ip_info:
            print(f"ISP: {ip_info.get('isp', '未知')}")
        if 'org' in ip_info:
            print(f"组织: {ip_info.get('org', '未知')}")
        if 'asn' in ip_info:
            print(f"AS号: {ip_info.get('asn', '未知')}")
        if 'hostname' in ip_info:
            print(f"主机名: {ip_info.get('hostname', '未知')}")
        if 'domain' in ip_info:
            print(f"域名: {ip_info.get('domain', '未知')}")
    
    # 是否有安全信息
    has_security = any(key in ip_info for key in ['proxy', 'vpn', 'tor'])
    if has_security:
        print("\n----- 安全情报 -----")
        proxy_status = "是" if ip_info.get('proxy') else "否"
        vpn_status = "是" if ip_info.get('vpn') else "否"
        tor_status = "是" if ip_info.get('tor') else "否"
        
        print(f"是否代理: {proxy_status}")
        print(f"是否VPN: {vpn_status}")
        print(f"是否TOR: {tor_status}")
    
    # 打印其他可能的信息
    other_info = {}
    for key, value in ip_info.items():
        if key not in ['ip', 'ip_from_json', 'query_url', 'success', 'country', 'region', 'city', 'loc', 
                      'postal', 'timezone', 'isp', 'org', 'asn', 'hostname', 
                      'domain', 'proxy', 'vpn', 'tor', 'latitude', 'longitude']:
            other_info[key] = value
    
    if other_info:
        print("\n----- 其他信息 -----")
        for key, value in other_info.items():
            # 将key格式化为更可读的形式
            formatted_key = key.replace('_', ' ').title()
            print(f"{formatted_key}: {value}")
    
    # 如果没有找到任何有用信息，提示用户
    if not (has_location or has_network or has_security or other_info):
        print("\n未能提取到详细信息，可能由于网站结构变化或反爬限制。")
        print(f"请手动访问: {ip_info.get('query_url', 'https://iplark.com')}")
    
    # 显示当前查询的时间戳
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n查询时间: {current_time}")
    
    return ip_info

def main():
    parser = argparse.ArgumentParser(description='获取指定IP地址的详细信息 (使用iplark.com)')
    parser.add_argument('ip', help='要查询的IP地址')
    
    # 如果没有提供参数，打印帮助信息
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)
    
    args = parser.parse_args()
    display_ip_info(args.ip)

if __name__ == "__main__":
    main()