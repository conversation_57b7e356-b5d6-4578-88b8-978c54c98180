def process_csv(input_file, output_file):
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            # 移除行尾的换行符
            line = line.strip()
            
            # 查找最后一个'/'的位置
            last_slash_index = line.rfind('/')
            
            if last_slash_index != -1:
                # 提取最后一个斜杠之后到行尾的内容
                extracted_content = line[last_slash_index + 1:]
                outfile.write(extracted_content + '\n')
            else:
                # 如果没有找到斜杠，保留整行
                outfile.write(line + '\n')

# 调用函数处理文件
input_file = 'actions.csv'
output_file = 'processed_actions.csv'
process_csv(input_file, output_file)

print(f"处理完成！结果已保存到 {output_file}") 