import re

def process_dependencies(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        # 读取文件内容并去除首尾的方括号
        content = f.read().strip('[]')
        
        # 使用正则表达式分割依赖项（处理可能存在的换行和空格）
        dependencies = re.split(r',\s*', content)
        
        # 过滤并清理依赖项
        filtered = []
        for dep in dependencies:
            # 去除首尾引号和空格
            cleaned = dep.strip('" \n')
            if cleaned and not cleaned.startswith("com.hellobike"):
                filtered.append(cleaned)
                
        # 写入结果文件
        with open(output_file, 'w', encoding='utf-8') as out_f:
            out_f.write('\n'.join(filtered))

if __name__ == "__main__":
    input_file = "long_text_2025-04-10-10-44-37.txt"
    output_file = "filtered_dependencies.txt"
    process_dependencies(input_file, output_file)
    print(f"处理完成！结果已保存到 {output_file}") 