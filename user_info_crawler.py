import requests
import csv
import time
import random

# 配置参数
headers = {
    'Host': 'admin.msgpetiversal.com',
    'Usb': 'user',
    'Ba-User-Token': 'edd6aee3-6c67-49aa-8456-c4501e3349f6',
    'Content-Type': 'application/json; charset=UTF-8',
    'Scene': 'minapp',
    'Server': '1',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x1800392b) NetType/WIFI Language/zh_CN'
}

api_url = 'https://admin.msgpetiversal.com/api/dyr.Dyruser/getDyrInfo'
output_file = 'user_info.csv'
start_user_id = 1
max_retries = 3  # 最大重试次数
retry_delay = 10  # 重试等待时间(秒)

def save_to_csv(data):
    with open(output_file, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['nickname', 'mobile', 'city', 'address'])
        if f.tell() == 0:  # 如果文件为空，写入header
            writer.writeheader()
        writer.writerow(data)

def get_user_info(user_id):
    for attempt in range(max_retries):
        try:
            # 随机延迟1-3秒降低请求频率
            time.sleep(random.uniform(1, 5))
            
            payload = {'user_id': str(user_id)}
            response = requests.post(api_url, headers=headers, json=payload, timeout=10)
            
            # 检查HTTP状态码
            if response.status_code != 200:
                print(f'用户ID {user_id} 请求失败，状态码：{response.status_code}')
                return None
                
            json_data = response.json()
            
            # 检查有效数据
            if not json_data.get('data'):
                print(f'用户ID {user_id} 无有效数据')
                return False
                
            # 提取所需字段
            user_data = {
                'nickname': json_data['data'].get('nickname', ''),
                'mobile': json_data['data'].get('mobile', ''),
                'city': json_data['data'].get('city', ''),
                'address': json_data['data'].get('address', '')
            }
            
            return user_data
            
        except Exception as e:
            print(f'用户ID {user_id} 请求异常（尝试 {attempt+1}/{max_retries}）: {str(e)}')
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                return None

def main():
    user_id = start_user_id
    empty_count = 0  # 连续空数据计数器
    
    while True:
        result = get_user_info(user_id)
        
        if result is None:  # 请求失败
            break
            
        if result is False:  # 无数据
            empty_count += 1
            if empty_count >= 5:  # 连续5次无数据则停止
                print("连续5次无数据，停止采集")
                break
            user_id += 1
            continue
            
        # 保存有效数据
        save_to_csv(result)
        print(f'成功保存用户ID {user_id} 的数据')
        user_id += 1
        empty_count = 0  # 重置计数器

if __name__ == '__main__':
    main() 