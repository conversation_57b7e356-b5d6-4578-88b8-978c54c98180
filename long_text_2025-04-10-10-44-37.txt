["com.tencent.tinker:tinker-android-anno:*********", "com.hellobike.moped:business-internal:6.83.01", "com.hellobike.doraemonkit:hellotravel-extension:6.82.0", "com.hellobike.ads:library-ads-csj:********", "com.evehicle.business:business-usevehicle:6.67.5.07091605", "com.hellobike.middle:middle-subscribebundle:6.70.0", "com.hellobike.library:evehicle-sale:6.67.5.07091605", "com.hellobike.android.moped:fmap:6.1.0", "com.hellobike.business:bussiness-advflowbundle:1.2.1", "com.hellobike.business:business-hitchbundle:6.85.4", "com.hellobike.driverauthmethod:service-driverauthmethod-library:6.52.0", "com.hellobike.business:business-ecodriverbundle:6.85.0", "com.hellobike.service:service-usergrowth-hitchpax:6.81.0", "com.hellobike.business:hitch-common:6.85.0", "com.hellobike.maphitch:map-business-hitch:6.85.0", "com.hellobike.vehiclemap:map-business-eco:6.85.0", "com.hellobike.service:service-productcenterbundle:6.82.0", "com.hellobike.eco:eco-middle-business:6.84.0", "com.hellobike.business:business-app:6.85.0", "com.hellobike.business:business-homepagebundle:********", "com.hellobike.business:business-poi:6.85.0", "com.hellobike.middle:hitch-securitycenter:6.66.2", "com.hellobike.vehiclemap:map-business-poi:6.85.0", "com.hellobike.vehiclemap:map-common:6.85.0", "com.hellobike.middle:poi-bundle:6.78.0", "com.hellobike.middle:ph-car-middlebundle:6.52.0", "com.hellobike.sparrow:sparrow-preferences:6.15.1", "com.hellobike.business:business-userbundle:********", "com.hellobike.hlph:vehicle-dynamic-view:6.82.0", "com.hellobike.business:business-travelbundle:6.52.0", "com.hellobike.business:business-orderbundle:********", "com.hellobike.common:common-advertbundle:6.85.0", "com.hellobike:library-h5offline:6.47.0", "com.hellobike.vehicle:vehicle-cti-common:1.8.0", "com.hellobike.service:service-ecopaxbundle:6.72.0", "com.hellobike.service:service-ecodriverbundle:6.84.0", "com.hellobike.business:usergrowth-middle-business:6.85.0", "com.hellobike.service:service-hitchbundle:6.85.0", "com.hellobike.service:service-carpooldriverbundle:6.84.0", "com.hellobike.vehicle:vehicle-middle-evaluation:6.53.0", "com.hellobike.eco:eco-middle-core:6.52.0", "com.hellobike.vehicleplatform:vehicleplatform:6.81.0", "com.hellobike.service:service-intercitypaxbundle:6.84.0", "com.hellobike.hlph:biz-toolkit:6.84.0", "com.hellobike.moped:moped-platform:6.85.2", "com.hellobike.platform:tools-scan:********", "com.hellobike.business:business-momentsbundle:********", "com.chengle.lib:game-lib:6.74.0", "com.hellobike:library-messagekit:6.77.0", "com.hellobike:library-rewardad:1.1.5", "com.hellobike.middle:securitycenter:6.77.0.0", "com.hellobike.middle:eco-securitycenter:6.55.4", "com.hellobike.middle:middle-photolib:6.54.0", "com.hellobike.middle:library-livenotice:6.73.0", "com.hellobike.middle:middle-pushbundle:6.83.0", "com.hellobike.vehicle:vehicle-cti:6.85.0", "com.hellobike.vehiclemap:map-middle:6.85.0", "com.hellobike.oho:hybrid-ui-amap:2.0.6", "com.hellobike.fmap:fmap_amap:6.2.3", "com.hellobike:library-mapnavi:6.82.0", "com.hellobike.vehicle:vehicle-middle-amapnavi:6.85.0", "com.hellobike.vehicle:vehicle-dialog-manager:6.54.0", "com.hellobike:middle-swipecaptcha:6.64.3", "com.hellobike.map:map-hl-sctx:6.85.0", "com.hellobike.middle:middle-imcallbundle:6.85.0", "com.hellobike.vehicle:vehicle-syncchanel-cmdmanager:6.85.1", "com.hellobike.flutter:hb_flutter_platform:3.5.0", "com.hellobike.middle:middle-imbundle:6.85.0", "com.hellobike.vehicle:vehicle-ui:6.85.0", "com.hellobike.map:map-hl-recommend:6.85.0", "com.hellobike:library-taskcenter:1.1.3", "com.hellobike.business:business_rebate_bundle:6.81.0", "com.hellobike:library-identityVerify:6.64.0", "com.hellobike.library:library-platformopen:*******", "com.cheyaoshi:ckshare:6.52.0", "com.hellobike.business:middle_hblink:********", "com.hellobike.vehicle:vehicle-mvi-ktx:1.3.3", "com.hellobike.business:business-unpaid:6.85.0", "com.hellobike:library-lego-service:*********", "com.hellobike.hlph:trajectory-report:6.85.1", "com.hellobike.business:business_switch_city:*******", "com.hellobike.map:map-hl-location:6.85.0", "com.hellobike.hlph:biz-tcp-kit:6.85.0", "com.hellobike:library-hotfix:6.57.0", "com.hellobike.sparrow:sparrow-exception:6.15.0", "com.hellobike:library-apm:6.68.0", "com.hellobike.sparrow:sparrow-third-auth:6.53.0", "com.hellobike:library-alipayauth:6.52.0", "com.hellobike:library-cmccauth:6.52.0", "com.hellobike:library-mobtechauth:6.52.0", "com.hellobike:library-thirdpartyauth:********", "com.hellobike:library-authtype:6.52.0", "com.hellobike:hlrouter_extension:5.85.0", "com.hellobike:hlrouter:6.11.0", "com.hellobike.logger:logger_extension:********", "com.hellobike:library-versionupdate:********", "com.hellobike.service:service-homepagebundle:********", "com.hellobike:library-announcement:0.0.4", "com.hellobike.vehicle:vehicle-middle-configdata:6.83.0", "com.hellobike:library-sinan:6.84.0", "com.hellobike.service:service-bikebundle:6.52.0", "com.hellobike.vehicle:vehicle-interfacemerge:********", "com.hellobike.map:map-lbs-service:6.85.0", "com.hellobike.middle:middle-accountinfo:6.52.0", "com.hellobike.service:service-changebatterybundle:6.67.0", "com.hellobike.moped:middle-api:6.15.5", "com.hellobike.sparrow:sparrow-location:6.15.0", "com.hellobike.middle:middle-mapbundle:6.85.0", "com.hellobike.middle:middle-facebundle:6.68.8", "com.hellobike.middleware:middleware-tablibrary:********", "com.hellobike:library-wechatauth:6.75.0", "com.hellobike.moped:common-base:6.52.0", "com.hellobike.sparrow:sparrow-share:6.15.0", "com.hellobike.middlemoped:middlemoped-absdk:6.52.0", "com.hellobike.middle:middle-bundlelibrary:6.76.0.0", "com.hellobike:library-lego:10.0.28.1", "com.hellobike.service:service-ebikebundle:6.82.01", "com.hellobike:library-flutter-ads:1.16.0", "com.hellobike:library-ads:********", "com.hellobike.oho:hybrid-ui-service-c:0.0.1", "com.hellobike:library-hybrid-service-pay:6.85.0", "com.hellobike.hybrid:hybrid:6.23.0", "com.hellobike.android.moped:fmap-extends:6.71.0", "com.hellobike.library:library-evehiclenfc:6.50.5", "com.hellobike.business:business_platform_travelbundle:6.85.5", "com.hellobike.library:easy-expose:1.0.1", "com.hellobike.middlemoped:middlemoped-mocktool:2.0.0", "com.hellobike.business:business-changebatterybundle:6.80.0", "com.hellobike.rm:ride-internal:6.83.01", "com.hellobike.doraemonkit:chuck:6.48.0", "com.hellobike.qigsaw:library-component-dynamic:2.3.0.4", "com.hellobike.qigsaw:splitcore:2.0.0", "com.hellobike.qigsaw:splitinstaller:2.3.0.2", "com.hellobike:library-helloscan:6.83.0.0", "com.hellobike.qigsaw:playcorelibrary:2.3.0.0", "com.hellobike.qigsaw:splitloader:2.3.0.1", "com.hellobike:scancode:6.83.0.0", "com.hb.ride.ocr:ocr:1.5.1.2", "com.hellobike.library:dynamic-load:6.77.0", "com.hellobike.library:library-filedownload:1.25.0", "com.hellobike:library-spdbpay:2.2.0", "com.hellobike:library-promo:6.76.0", "com.hellobike.vehicle:permission-hooker:6.4.0", "com.hellobike.business:business-stationdriverbundle:6.81.0", "com.chengle.lib:game-csj:1.2.1", "com.chengle.lib:game-core:1.0.9", "com.hellobike.rm:ride-common:6.52.0", "com.hellobike.business:business-intercitypaxbundle:6.85.0", "com.hellobike:library-cache:1.2.1", "com.hellobike:library-sparrow-pay:6.53.0", "com.hellobike:library-pre-cashier:6.83.0", "com.hellobike:library-magiccube:3.12.0", "com.hellobike:library-deviceinfo:6.81.0.0", "com.hellobike.sparrow:sparrow-network:7.0.0", "com.hellobike:library-dyconfig:6.70.0.2", "com.hellobike:library-gateway:6.27.0", "com.hellobike.bifrost:bifrost_jsbridge_service:6.75.8", "com.hellobike:library-startupprotect:1.4.3.0", "com.hellobike.library:service-dynamic-load:0.0.1", "com.hellobike.abtest:abtest-core:2.2.3", "com.hellobike.middle:middle-paybundle:6.15.5", "com.cheyaoshi:ckpay:1.2.4", "com.hellobike:library-securitykit:6.50.5", "com.hellobike:library-allpay:6.85.0", "com.hellobike:library-pay-core:6.85.0", "com.hb.ride:network_image_kit:0.0.7", "com.hellobike:library-pay-sign:********", "com.hellobike:library-pay-base:6.85.0", "com.hellobike:library-networking:6.15.5", "com.hellobike.qigsaw:splitextension:2.0.0", "com.hellobike.qigsaw:splitrequester:2.0.0", "com.hellobike.qigsaw:splitreporter:2.0.0", "com.hellobike.qigsaw:splitcommon:2.3.0.0", "com.hellobike.sparrow:sparrow-tangram:6.15.0", "com.hellobike.sparrow:sparrow-configcenter:6.15.0", "com.hellobike:library-tangram:0.2.11", "com.hellobike.service:service-poi:6.77.0", "com.hellobike:library_netcore:6.52.0", "com.hellobike.sparrow:sparrow-user:********", "com.hellobike.service:service-userbundle:6.85.0", "com.hellobike.hlph:tcp-kit:6.85.3", "com.hellobike.common:commonbundle:6.52.0", "com.cheyaoshi:cknetworkingV2:6.52.0", "com.hellobike:library-netcrypto:6.30.0", "com.hellobike:library-devicefingerprint:6.66.6", "com.hellobike:library-startup:6.21.0", "com.hellobike.fluxhellobikemodule:flux_hellobike_module:1.2694.307226", "com.hellobike.flutter.3rdaar:lumos:1.0.4", "com.hellobike.hlph:biz-viewkit:6.85.0", "com.hellobike.ads:library-ads-thirdcore:********", "com.hellobike.ads:library-ads-base:********", "com.hellobike.sparrow:sparrow-analytics:6.15.0", "com.hellobike.sparrow:sparrow-invocation:6.26.0", "com.hellobike.sparrow:sparrow:2.2.1", "com.hellobike.sparrow:sparrow-gateway:6.26.0", "com.hellobike:library_hl_platform_serviceloader:1.2.0", "com.hellobike:library-hiubt:6.70.5", "com.hellobike:library-executor:6.15.5", "com.hellobike:library-thread-pool:1.0.0", "com.evehicle.library:library-locationrecord:6.50.5", "com.hellobike.moped:business-sdk:6.84.01", "com.hellobike.middle:security-ui:6.50.5", "com.hellobike.middle:hmui:********", "com.cheyaoshi:ckcropimage:1.1.6", "com.hellobike:library-imageloader:6.53.0", "com.hellobike.business:business-evehiclebundle:6.79.0", "com.hellobike:library-umspppay:2.2.0", "com.hellobike:library-psbcpay:2.3.0", "com.hellobike.vehicle:vehicle-tools:6.52.0", "com.hellobike.middle:middle-routerprotocol:1.1.9", "com.hellobike:library-router:6.52.0", "com.hellobike:library-cmbcpay:2.3.0", "com.hellobike.service:service-securitycenter:6.50.5", "com.hellobike.logger:logger_protocol:6.52.0", "com.hellobike.business:business-vvsmartbundle:6.79.0", "com.hellobike.rm:ride-business-ride-status-unify:6.52.0", "com.hellobike.rm:library_ride_status_base:6.17.0", "com.hellobike.library:okdownload-breakpoint-sqlite:1.0.5", "com.hellobike:library-encrypt:1.0.16", "com.hellobike.middlemoped:middlemoped-msghandlerbundle:1.0.0", "com.hellobike.business:business-liftpublishbundle:6.85.0", "com.hellobike.business:business-transitcodebundle:6.53.0", "com.hellobike.middlemoped:middlemoped-ridecardbundle:6.63.0", "com.hellobike.service:service-facebundle:6.50.5", "com.evehicle.business:business-sale:6.67.5.07091605", "com.hellobike.service:service-liftpublishbundle:6.84.0", "com.hellobike.sparrow:sparrow-platformview:6.26.0", "com.hellobike.rm:ride-business-common:6.52.0", "com.hellobike.business:business_playlet_bundle:*******", "com.hellobike.service:service-transitcodebundle:********", "com.hellobike.business:business-financebundle:6.75.5", "com.hellobike.business:business-usergrowth-hitchpax:6.85.0", "com.hellobike.library:evehicle-lock:6.66.0", "com.hellobike:library-swipecaptcha:6.50.5", "com.hellobike.bifrost:hellobike_bifrost_debug:6.73.0", "com.cheyaoshi:ckwxjarcomponent:2.0.0", "com.hellobike.business:business-ecoexpressbundle:6.85.0", "com.hellobike.hlph:diagnose-trajectory-report:6.85.0", "com.hellobike:library-filemanager:6.50.5", "com.hellobike.map:map-hl-collection:6.62.0", "com.hellobike:library-exoplayer:0.0.2", "com.hellobike.middlemoped:middlemoped-imageloaderbundle:6.52.0", "com.hellobike.rm:ride-business-operatelock:6.71.0", "com.hellobike.bifrost:bifrost_jsbridge:6.85.2", "com.hellobike:library-expose:0.1.3", "com.hellobike.logger:logger_storage:6.52.0", "com.hellobike:library-hlog:6.18.5", "com.hellobike.bifrost:hellobike_service_bifrost:6.64.0", "com.hellobike.service:service-platform:********", "com.hellobike.storage:library-storage:6.4.0", "com.hellobike.map:map-hl-map:6.83.0", "com.hellobike.storage:library-storage-service:6.4.0", "com.hellobike.hlph:diagnose-viewkit:1.2.0", "com.hellobike.business:business-bikebundle:6.84.01", "com.hellobike.business:business-productcenterbundle:6.85.0", "com.hellobike:library-cmbpay:2.7.0", "com.hellobike.middle:pu<PERSON>-sparrow:6.85.0", "com.hellobike.map:map-hl-splash:6.84.0", "com.hellobike.ride.components:ridemobile_components:6.83.01", "com.hellobike.feature:evc-find:6.67.5.07091605", "com.hellobike.business:business-lifebundle:6.15.5", "com.hellobike.library:evehiclemiddle:6.79.0", "com.hellobike:library-cuqppay:2.13.0", "com.hellobike.business:business-hitch-driver:6.85.1", "com.hellobike.business:business-evehicleuibundle:6.67.5.07091605", "com.hellobike:library-adapter-compose:********", "com.hellobike.business:business-trainticketbundle:6.54.0", "com.hellobike.library:okdownload-filedownloader:1.0.5", "com.hellobike.library:okdownload-connection-okhttp:1.0.5", "com.hellobike.library:okdownload:1.0.5", "com.hellobike.bifrost:hellobike_bifrost:6.85.0", "com.hellobike:library-aliauth:6.55.0", "com.hellobike.vehicle:diagnose-hitchbundle:6.84.0", "com.hellobike.business:business-carpooldriverbundle:6.85.0", "com.hellobike.rm:library_bluetooth:6.58.0", "com.hellobike.business:business-platform-balance:6.85.0", "com.hellobike.ads:library-ads-gdt:********", "com.hellodriver.business:driverauth_library:6.85.0", "com.hellobike.logger:logger_upload_page:6.52.0", "com.evehicle.library:library-evehicle-vehicledata:6.66.0", "com.hellobike.rm:multi_ble:1.0.0", "com.hellobike.fmap:fmap-protocol:6.1.6", "com.hellobike:library-abcpay:2.3.0", "com.hellobike:library-bocpay-code:2.3.0", "com.hellobike:library-ccbpay:2.4.0", "com.hellobike:library-thirdpartyalipay:2.3.0", "com.hellobike:library-thirdpartypay:2.14.0", "com.hellobike:library-thirdpartypay-common:2.14.0", "org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72", "com.hellobike.doraemonkit:doraemonkit:1.2.8", "com.hellobike:library-jdpay:2.9.0", "com.hellobike:library-thirdpartyauth-annotation:********", "com.hellobike.doraemonkit:hellotravel-extension-noop:0.0.9", "com.evehicle.library:library-evehicle-base:6.55.0", "com.hellobike.oho.flutter.hb_platform_permission_plugin:hb_platform_permission_plugin:0.0.1", "com.hellobike.oho:web-container:2.0.8", "com.squareup.okhttp3:okhttp:********", "com.hellobike:library_homepreload:0.0.1", "com.hellobike:annotation_startup:1.0.0", "com.hellobike.service:service-vvsmartbundle:6.15.5", "com.hellobike.bifrost:bifrost-annotation:1.0.3", "com.hellobike:module-init:6.83.0", "com.hellobike.doraemonkit:ubtLog:6.0.1", "com.hellobike:library-bocpay:0.0.3", "com.hellobike.skin:skin-appcompat:1.0.0", "com.hellobike.business:uber-h3:6.52.0", "com.hellobike.flutter:contacts_service:1.0.0", "com.hellobike:library-pay-config:6.85.0", "com.hellobike.vehicle:vehicle-jsonpatch:1.0.0", "com.hellobike:hlrouter_base:1.1.0", "com.hellobike.vehicle:vehicle-mvi:1.2.4", "com.hellobike:library-nettoolkit:1.0.6", "com.hellobike.vehiclemap:map-business-service:6.75.0", "com.hellobike.common:lib-imodule:1.1.0", "com.hellobike.middle:middle-dbbundle:6.61.1", "com.hellobike.appmainres:easybike-appicon:6.85.0.202503252152", "com.evehicle.library:library-evehicle-lockbiz:6.55.0", "com.hellobike.hybrid:hybrid-annotation:1.0.1", "com.haluotrip.bos.flutter:router-core:0.1.1", "com.hellobike.sparrow:sparrow-annotation:1.0.11", "com.hellobike.qigsaw:splitdownloader:2.0.0", "com.hellobike:hlenvironment:1.1.4", "com.hellobike:hllogger:6.19.0", "com.hellobike:retrofit2:0.0.5", "com.hellobike:hlrouter_annotation:1.0.0", "com.hellobike.doraemonkit:base-extension:1.2.7", "com.hellobike.library:cheatingdetection:0.0.6", "com.cheyaoshi:jackson:1.0.0", "com.hellobike:library-bobpay:2.2.0", "com.hellobike.oho:web-container-service:0.0.1", "com.hellobike.skin:skin-support:1.0.0", "com.hellobike.sparrow:sparrow-event:1.0.3", "com.hellobike.platform:butcherknife:0.0.4", "com.cheyaoshi:blelock:7.0.0", "com.hellobike:library-hiubt-verification:0.0.4-beta", "com.hellobike:library-dypay:1.3.0", "com.hellobike.skin:skin-constraint:1.0.0", "com.hellobike.skin:skin-recyclerview:1.0.0", "com.hellobike:library-bifrost-pay:6.69.0", "com.hellobike:library-alipay:0.0.4", "com.hellobike.service:service-momentsbundle:6.76.0", "com.hellobike.bifrost:bifrost-user:6.30.0", "com.hellobike.service:service-account:0.0.2", "com.hellobike:library_actionqueue:6.15.5", "com.hellobike.vehicle:vehicle-dynamic-tts:6.85.0", "com.hellobike.service:service-travelbundle:0.0.8", "com.haluotrip.component:logger:6.18.0", "com.airbnb.android:lottie:*******", "com.hellobike.common:common-andpermission:********", "com.hb.flutter.ride.hb_ride_flutter_provider:hb_ride_flutter_provider:1.1.0", "com.hellobike:library-thirdpartypay-annotation:1.1.0", "com.hellobike.skin:skin-design:1.0.0", "com.hellobike.service:service-evehiclebundle:0.0.2", "com.hellobike:library_hl_platform_startuploader:1.0.0", "com.hellobike.business:business-driverlifebundle:6.67.0", "com.hellobike:library-lego-engine:*********", "com.hellobike.skin:skin-cardview:1.0.0", "com.mpaas.android:mpaas-baseline:10.2.3-46", "com.mpaas.android:mriver:*********", "com.mpaas.android:nebula:*********", "com.mpaas.android:logging:*********", "com.mpaas.android:tinyapp:*********", "com.mpaas.android:storage:*********", "com.mpaas.android:video-player:*********", "com.mpaas.android:mriver-bluetooth:*********", "com.mpaas.android:uccore-dynamic:*********", "com.alipay.android.phone.wallet:nebulaucsdk-build:10.2.3.00000886", "com.alibaba.ariver:commonability-build:10.2.3.00001230", ":APSecuritySDK-DeepSec-7.0.1.20230530.ji<PERSON>:", ":3.8.1.16987380-CH-HELLOBIKE_ALL-proguard:", "com.hellobike.library:library-evehicleble:*******", "com.tencent.tinker:tinker-android-lib:*********", "com.tencent.tinker:tinker-android-anno-support:*********", "com.umeng.umsdk:common:9.6.3", "com.umeng.umsdk:apm:1.9.2", "com.umeng.umsdk:asms:1.8.0", "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.72", "org.jetbrains.anko:anko-commons:0.10.5", "com.google.android.material:material:1.2.0", "androidx.appcompat:appcompat:1.3.0", "com.sankuai.waimai.router:router:1.2.0", "com.github.bumptech.glide:glide:3.7.0", "com.kuaishou.koom:koom-java-leak:2.2.1", "com.squareup.leakcanary:leakcanary-android:2.9.1", "com.hellobike.flutter.3rdaar:flutter_boost:3.0.3", "com.tencent.matrix:matrix-android-lib:0.6.5", "io.flutter:flutter_embedding_release:1.0.0-890a5fca2e34db413be624fc83aeea8e61d42ce6", "com.squareup.okio:okio:1.17.2", "com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0", "com.google.android:flexbox:2.0.0", "org.greenrobot:eventbus:3.1.1", "com.github.bumptech.glide:okhttp3-integration:1.4.0", "com.tencent.matrix:matrix-trace-canary:0.6.5", "com.hellobike.library.3rdaar:open_ad_sdk:6.0.1.4", "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.3", "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.3", "com.czt.mp3recorder:library:1.0.4", "com.gyf.immersionbar:immersionbar:2.3.3", "androidx.constraintlayout:constraintlayout:1.1.3", "joda-time:joda-time:2.10.5", "no.nordicsemi.android.support.v18:scanner:1.6.1", "androidx.annotation:annotation:1.1.0", "com.google.code.gson:gson:2.8.5", "com.jiguang.sdk.push:oppo:3.1.0", "commons-codec:commons-codec:1.6", "androidx.core:core-ktx:1.5.0", "com.blankj:utilcode:1.30.7", "com.alibaba:<PERSON><PERSON><PERSON>:1.2.62", "com.getui:gtc:3.2.8.0", "com.getui:gs:1.4.2.0", "androidx.room:room-ktx:2.1.0", "org.jetbrains.anko:anko-appcompat-v7-commons:0.10.5", "androidx.lifecycle:lifecycle-runtime-ktx:2.2.0", "androidx.lifecycle:lifecycle-livedata-ktx:2.2.0", "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0", "androidx.lifecycle:lifecycle-common-java8:2.1.0", "com.meituan.android.walle:library:1.1.6", "androidx.lifecycle:lifecycle-extensions:2.1.0", "androidx.percentlayout:percentlayout:1.0.0", "com.google.auto.service:auto-service:1.0-rc2", "com.hellobike.library.3rdaar:ali-oss-android-sdk:2.9.2", "com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.30", "com.contrarywind:wheelview:4.1.0", "com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-12", "com.hellobike.library.3rdaar:logging-interceptor:3.12.0", "pl.droidsonroids.gif:android-gif-drawable:1.2.19", "com.kk.taurus.playerbase:playerbase:*******", "androidx.room:room-runtime:2.1.0", "androidx.multidex:multidex:2.0.0", "com.github.razerdp:BasePopup:2.2.10", "com.alipay.android.phone.wallet:nebula-build:10.2.3.565", "com.alipay.android.phone.mobilesdk:framework-build:10.2.3.650", "com.alipay.android.phone.mobilesdk:logging-build:*********7.46", "org.jetbrains.kotlin:kotlin-stdlib:1.6.10", "androidx.asynclayoutinflater:asynclayoutinflater:1.0.0", "androidx.recyclerview:recyclerview:1.0.0", "androidx.legacy:legacy-support-v4:1.0.0", "com.jakewharton:butterknife:10.2.0", "com.github.Raizlabs.DBFlow:dbflow:3.0.1", "androidx.cardview:cardview:1.0.0", "io.reactivex.rxjava2:rxandroid:2.1.0", "io.reactivex.rxjava2:rxjava:2.2.0", "com.github.Raizlabs.DBFlow:dbflow-core:3.0.1", "commons-io:commons-io:2.4", "com.makeramen:roundedimageview:2.3.0", "com.google.zxing:core:3.3.3", "com.shizhefei:ViewPagerIndicator:1.1.9.androidx", "com.umeng.umsdk:uyumao:1.1.4", "androidx.databinding:viewbinding:4.1.3", "androidx.viewpager2:viewpager2:1.0.0", "androidx.work:work-runtime:2.2.0", "androidx.gridlayout:gridlayout:1.0.0", "androidx.lifecycle:lifecycle-livedata-core-ktx:2.2.0", "androidx.lifecycle:lifecycle-common:2.3.0", "com.xingin:xhssharesdk:1.1.4", "androidx.core:core:1.5.0", "com.google.android.exoplayer:exoplayer-core:2.12.1", "com.oushangfeng:PinnedSectionItemDecoration:1.2.6", "com.romandanylyk:pageindicatorview:1.0.3", "com.github.MasayukiSuda:Mp4Composer-android:v0.4.1", "org.apache.commons:commons-math3:3.6.1", "com.github.mmin18:realtimeblurview:1.2.1", "com.google.android.filament:filament-android:1.20.0", "com.google.android.filament:filament-utils-android:1.20.0", "com.google.android.filament:gltfio-android:1.20.0", "com.danikula:videocache:2.7.1", "com.huawei.hms:opendevice:6.3.0.305", "com.huawei.hms:availableupdate:6.5.0.300", "com.huawei.hms:stats:6.5.0.300", "com.huawei.hms:device:6.5.0.300", "com.huawei.hmf:tasks:1.4.1.300", "cn.jiguang.sdk:jpush:5.0.7.0", "cn.jiguang.sdk:jcore:4.2.4.0", "com.huawei.hms:ui:6.5.0.300", "com.huawei.hms:log:6.5.0.300", "com.huawei.hms:push:6.5.0.300", "cn.jiguang.sdk.plugin:hua<PERSON>:5.0.7.0", "cn.jiguang.sdk.plugin:oppo:5.0.7.0", "cn.jiguang.sdk.plugin:vivo:5.0.7.0", "cn.jiguang.sdk.plugin:meizu:5.0.7.0", "cn.jiguang.sdk.plugin:xiaomi:5.0.7.0", "cn.jiguang.sdk.plugin:honor:5.0.8", "com.jiguang.sdk.push:honor:7.0.61.302", "com.getui:gtsdk:3.3.1.0", "org.jetbrains.kotlin:kotlin-parcelize-runtime:1.6.10", "com.hellobike.library.3rdaar:AMap3DMap_SCTXS:2024.07.18", "com.hellobike.library.3rdaar:geetest_captcha_android:*******.2", "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72", "androidx.fragment:fragment:1.2.5", "androidx.activity:activity:1.2.3", "androidx.lifecycle:lifecycle-runtime:2.3.0", "androidx.collection:collection:1.1.0", "com.duoduo.sdk:opensdk:0.0.1", "com.umeng.umsdk:link:1.2.0", "com.google.guava:guava:27.1-android", "com.tencent.tinker:tinker-android-loader:*********", "com.google.protobuf:protobuf-java:3.9.0", "com.hellobike.library.3rdaar:alipaySdk:15.8.16", "com.hellobike.library.3rdaar:cmccauth:*******", "com.mob.auth:secverify:3.4.7", "com.facebook.soloader:soloader:0.10.3", "com.hellobike.library.3rdaar:tencent-WbCloudFaceSimpleLiveSdk:*******", "com.hellobike.library.3rdaar:tencent-WbCloudNormal:5.1.0", "com.trello.rxlifecycle2:rxlifecycle-android-lifecycle-kotlin:2.2.2", "com.trello.rxlifecycle2:rxlifecycle-android-lifecycle:2.2.2", "com.trello.rxlifecycle2:rxlifecycle-android:2.2.2", "com.uber.autodispose:autodispose-rxlifecycle:1.1.0", "com.alibaba.android:vlayout:1.2.32", "com.st:st25sdk:1.7.0", "com.st:st25android:1.0.7", "androidx.viewpager:viewpager:1.0.0", "com.huawei.hms:network-grs:7.0.2.301", "com.huawei.hms:hatool:6.11.0.301", "com.huawei.android.hms:security-ssl:1.2.0.307", "com.huawei.hms:network-common:7.0.2.301", "com.huawei.hms:network-framework-compat:7.0.2.301", "com.huawei.android.hms:security-base:1.2.0.306", "com.huawei.hms:dynamic-api:1.0.23.300", "com.huawei.agconnect:agconnect-core:1.7.2.300", "com.huawei.hms:ml-computer-commonutils-inner:3.8.0.303", "com.huawei.hms:ml-computer-agc-inner:3.8.0.303", "com.huawei.android.hms:security-encrypt:1.2.0.307", "com.huawei.hms:ml-computer-sdkbase-inner:3.8.0.303", "org.opencv:opencv:4.9.0", "androidx.localbroadcastmanager:localbroadcastmanager:1.0.0", "com.hellobike.library.3rdaar:spdb_pay:1.0.3", "com.swift.sandhook:hooklib:4.2.1", "com.swift.sandhook:xposedcompat:4.2.1", "io.github.taoweiji.quickjs:quickjs-android:1.4.6", "com.facebook.yoga.android:yoga-layout:1.19.0", "com.facebook.fbjni:fbjni:0.3.0", "com.bun:miitmdid:2.3.0", "org.jetbrains.kotlin:kotlin-reflect:1.3.72", "com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2", "com.squareup.retrofit2:adapter-rxjava2:2.3.0", "com.netease:netseckit:4.3.1.1", "com.netease:mobsecKit:1.8.3", "com.example.FlutterToast:fluttertoast:8.1.4", "com.hellobike.flutter.3rdaar:permission_handler:8.2.6", "io.flutter.plugins.pathprovider:path_provider_android:2.0.11", "com.tekartik.sqflite:sqflite:2.0.2", "io.flutter.plugins.deviceinfo:device_info:2.0.3", "io.flutter.plugins.urllauncher:url_launcher_android:6.0.15", "io.flutter.plugins.sharedpreferences:shared_preferences:2.0.7", "com.hellobike.flutter.3rdaar:flutter_refresh_rate:2.0.0", "io.flutter:armeabi_v7a_release:1.0.0-890a5fca2e34db413be624fc83aeea8e61d42ce6", "io.flutter:arm64_v8a_release:1.0.0-890a5fca2e34db413be624fc83aeea8e61d42ce6", "io.flutter:x86_64_release:1.0.0-890a5fca2e34db413be624fc83aeea8e61d42ce6", "androidx.vectordrawable:vectordrawable-animated:1.1.0", "com.hellobike.library.3rdaar:umspppay:*******", "no.nordicsemi.android:dfu:1.12.0", "com.mpaas.android:scan:*********", "androidx.lifecycle:lifecycle-process:2.3.1", "com.hellobike.library.3rdaar:cmbsdk-release:1.1.8", "com.github.zhpanvip:bannerviewpager:3.5.5", "com.hellobike.library.3rdaar:unionpay:0.0.1", "androidx.camera:camera-lifecycle:1.0.0-rc01", "androidx.camera:camera-view:1.0.0-alpha20", "androidx.camera:camera-camera2:1.0.0-rc01", "com.hellobike.library.3rdaar:GDTSDK:4.610.1480", "com.hellobike.library.3rdaar:abcbankcaller:0.0.1", "com.hellobike.library.3rdaar:ccbnetpay:2.4.1", "com.scottyab:rootbeer-lib:0.0.7", "com.amitshekhar.android:debug-db:1.0.6", "com.amitshekhar.android:debug-db-encrypt:1.0.6", "com.hellobike.library.3rdaar:jdpay:3.00.02", "com.google.code.findbugs:jsr305:3.0.2", "org.codehaus.mojo:animal-sniffer-annotations:1.17", "com.netease:ldnet_diagnoservice:1.0.0", "com.hellobike.library.3rdaar:bobpay:*******", "com.hellobike.library.3rdaar:dypay:*******", "com.aliyun.ams:alicloud-android-utdid:*******", "com.alipay.android.phone.scancode:bqcscanservice-build:10.2.3.498", "com.alipay.android.phone.scancode:mascanengine-build:10.2.3.498", "com.alipay.android.phone.wallet:scanexport-build:10.2.3.623", "com.alipay.android.mpaas.aar:mpaas-resources-api:1.1.0", "com.mpaas.mobile:metainfo-annotations:1.3.1", "com.alipay.android.inside:baselib:10.2.3.700", "com.alipay.android.phone.mobilesdk:autotracker-build:*********", "com.alipay.android.phone.mobilesdk:tianyanadapter-build:10.2.3.373", "com.alipay.android.phone.mobilesdk:monitor-build:10.2.3.650", "com.mpaas.mas.adapter:mpaasmasadapter-build:10.2.3.593", "com.alipay.android.phone.mobilesdk:quinox-build:10.2.3.378", "com.mpaas.android.adapter:utdid-adapter:*********5", "com.alipay.android.phone.mobilesdk:rpc-build:10.2.3.660", "com.alipay.android.phone.mobilesdk:transport-build:10.2.3.660", "com.alipay.android.phone.mobilesdk:netsdkextdependapi-build:10.2.3.388", "com.alipay.android.phone.mobilesdk:netsdkextdepend-build:10.2.3.373", "com.alipay.android.phone.thirdparty:securityguard-build:*********", "com.mpaas.mpaasadapter:commonbiz-build:*********", "com.alipay.android.phone.mobilecommon:dynamicrelease-build:**********", "com.alipay.android.phone.mobilesdk:commonbizservice-build:10.2.3.670", "com.alipay.android.phone.mobilesdk:storage-build:**********", "com.alipay.android.phone.mobilesdk:commonservice-build:*********", "com.alipay.android.phone.mobilesdk:common-build:**********", "com.alipay.android.phone.wallet:antui-build:**********", "com.mpaas.thirdparty:wire-build:*********", "com.mpaas.sqlcryptosdk:sqlcryptosdk-build:10.2.3.493", "com.mpaas.mobileapi:mobileapi-build:*********", "com.mpaas.security:mpaassecurityadapter-build:10.2.3.339", "com.alipay.android.phone.mobilesdk:socketcraft-build:*********", "com.mpaas.mobile:commonui-build:*********", "com.alipay.android.phone.wallet:beephoto-build:10.2.3.378", "com.alipay.android.phone.wallet:beecapture-build:10.2.3.496", "com.alipay.android.phone.wallet:beehive-build:*********", "com.alipay.multimedia.base:basic-build:10.2.3.604", "com.alipay.android.phone.mobilecommon:multimedia-build:10.2.3.683", "com.alipay.android.phone.mobilecommon:multimediabiz-build:10.2.3.683", "com.alipay.xmedia:mediaadapter-build:**********", "com.alipay.mobile:alipaycookie-build:10.2.3.304", "com.alipay.android.phone.wallet:beevideo-build:10.2.3.683", "com.alipay.multimedia:xmedia-build:*********", "com.alibaba.ariver:ariver-build:10.2.3.697", "com.mpaas.mriver:mrivernebula-build:10.2.3.560", "com.mpaas.mriver:mriveruc-build:10.2.3.677", "com.mpaas.mriver:mriverengine-build:10.2.3.677", "com.mpaas.mriver:mriver-build:10.2.3.683", "com.alipay.android.phone.wallet:basicstl-build:*********", "com.mpaas.mriver:mriverresource-build:10.2.3.449", "com.mpaas.mriver:mriverjsapi-build:10.2.3.648", "com.mpaas.multimedia:multimediaadapter-build:*********", "com.alipay.xmedia.algorithm:algorithm-build:*********", "com.mpaas.mriver:mriverappxone-build:*********", "com.mpaas.mriver:mriverappxplus-build:10.2.3.604", "com.mpaas.isec:isecssl-build:*********", "com.mpaas.control:license-build:10.2.3.586", "com.mpaas.multimedia.base:codec-build:*********", "com.mpaas.mpaasadapter:mpaasadapter-build:**********", "com.alipay.mobile:nebulakernel-build:*********", "com.alipay.mobile:nebulaengine-build:*********", "com.mpaas.nebula.adapter:mpaasnebulaadapter-build:*********", "com.alipay.android.phone.wallet:nebulaappproxy-build:10.2.3.369", "com.alipay.android.phone.wallet:nebulaconfig-build:*********", "com.alipay.android.phone.wallet:nebulaappcenter-build:*********", "com.mpaas.framework.adapter:mpaasframeworkadapter-build:**********", "com.alipay.android.phone.wallet:aompfilemanager-build:10.2.3.304", "com.alipay.android.phone.mobilesdk:liteprocess-build:*********", "com.alipay.android.phone.wallet:aompdevice-build:*********", "com.alipay.android.phone.wallet:h5worker-build:*********", "com.alipay.android.phone.wallet:apble-build:*********", "com.alipay.android.phone.mobilecommon:adaptermap-build:*********", "com.alipay.android.phone.mobilecommon:map-build:*********", "com.alipay.android.phone.mobilecommon:lbs-build:**********", "com.alipay.android.phone.wallet:nebulauc-build:10.2.3.560", "com.mpaas.tinyapp.commonres:tinyappcommonres:*********", "com.mpaas.mriver:mriverbluetooth-build:**********", "com.mpaas.uc:dynamicuc-build:10.2.3.560", "com.tencent.tinker:tinker-commons:*********", "androidx.coordinatorlayout:coordinatorlayout:1.1.0", "androidx.transition:transition:1.4.1", "androidx.vectordrawable:vectordrawable:1.1.0", "androidx.annotation:annotation-experimental:1.0.0", "androidx.appcompat:appcompat-resources:1.3.0", "androidx.drawerlayout:drawerlayout:1.0.0", "androidx.cursoradapter:cursoradapter:1.0.0", "androidx.savedstate:savedstate:1.1.0", "com.sankuai.waimai.router:interfaces:1.2.0", "com.kuaishou.koom:koom-fast-dump:2.2.1", "com.kuaishou.koom:koom-monitor-base:2.2.1", "com.kuaishou.koom:xhook:2.2.1", "com.kuaishou.koom:kwai-android-base:2.2.1", "com.kuaishou.koom:shark:2.2.1", "com.squareup.leakcanary:leakcanary-object-watcher-android:2.9.1", "com.squareup.leakcanary:leakcanary-android-core:2.9.1", "androidx.tracing:tracing:1.0.0", "org.jetbrains.kotlin:kotlin-stdlib-common:1.6.10", "androidx.constraintlayout:constraintlayout-solver:1.1.3", "androidx.room:room-common:2.1.0", "org.jetbrains.anko:anko-support-v4-commons:0.10.5", "androidx.lifecycle:lifecycle-livedata:2.2.0", "androidx.lifecycle:lifecycle-viewmodel:2.3.1", "com.meituan.android.walle:payload_reader:1.1.6", "androidx.lifecycle:lifecycle-service:2.1.0", "androidx.arch.core:core-runtime:2.1.0", "androidx.arch.core:core-common:2.1.0", "com.google.auto:auto-common:0.3", "pl.droidsonroids:relinker:1.3.1", "androidx.sqlite:sqlite-framework:2.0.1", "androidx.sqlite:sqlite:2.0.1", "org.jetbrains:annotations:13.0", "androidx.legacy:legacy-support-core-ui:1.0.0", "androidx.media:media:1.0.0", "androidx.legacy:legacy-support-core-utils:1.0.0", "com.jakewharton:butterknife-runtime:10.2.0", "org.reactivestreams:reactive-streams:1.0.2", "androidx.lifecycle:lifecycle-livedata-core:2.3.1", "androidx.versionedparcelable:versionedparcelable:1.1.1", "com.google.android.exoplayer:exoplayer-common:2.12.1", "com.google.android.exoplayer:exoplayer-extractor:2.12.1", "com.huawei.hms:base:6.5.0.300", "androidx.loader:loader:1.0.0", "androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1", "com.google.guava:failureaccess:1.0.1", "org.checkerframework:checker-compat-qual:2.5.2", "com.google.errorprone:error_prone_annotations:2.2.0", "com.google.j2objc:j2objc-annotations:1.1", "com.facebook.soloader:annotation:0.10.3", "com.facebook.soloader:nativeloader:0.10.3", "com.trello.rxlifecycle2:rxlifecycle:2.2.2", "com.uber.autodispose:autodispose:1.1.0", "androidx.customview:customview:1.0.0", "com.swift.sandhook:hookannotation:4.2.1", "com.facebook.yoga:yoga:1.19.0", "androidx.interpolator:interpolator:1.0.0", "com.github.zhpanvip:viewpagerindicator:1.2.1", "androidx.camera:camera-core:1.0.0-rc01", "com.amitshekhar.android:debug-db-base:1.0.6", "com.tencent.tinker:aosp-dexutils:*********", "com.tencent.tinker:bsdiff-util:*********", "com.tencent.tinker:tinker-ziputils:*********", "com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.9.1", "com.squareup.leakcanary:shark-android:2.9.1", "com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.9.1", "com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.9.1", "androidx.slidingpanelayout:slidingpanelayout:1.0.0", "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0", "androidx.documentfile:documentfile:1.0.0", "androidx.print:print:1.0.0", "com.jakewharton:butterknife-annotations:10.2.0", "com.huawei.hms:baselegacyapi:6.5.0.300", "com.squareup.leakcanary:leakcanary-object-watcher:2.9.1", "com.squareup.leakcanary:leakcanary-android-utils:2.9.1", "com.squareup.leakcanary:shark:2.9.1", "com.squareup.leakcanary:shark-log:2.9.1", "com.squareup.leakcanary:shark-graph:2.9.1", "com.squareup.leakcanary:shark-hprof:2.9.1"]