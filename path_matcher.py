import sys

def extract_filename(path):
    # 从路径中提取最后一个'/'后的文件名
    parts = path.strip().split('/')
    
    # 先获取最后一个部分作为文件名
    filename = parts[-1]
    
    # 如果文件名包含 {}，则从右向左查找不包含 {} 的文件名
    index = len(parts) - 1
    while ('{' in filename or '}' in filename) and index > 0:
        index -= 1
        filename = parts[index]
        
    return filename

def match_files(benchmark_path, target_path):
    # 读取 benchmark.csv 文件
    with open(benchmark_path, 'r') as f:
        benchmark_lines = f.readlines()
    
    # 读取待匹配文件
    with open(target_path, 'r') as f:
        target_lines = f.readlines()
    
    results = []
    
    # 对 benchmark.csv 的每一行进行处理
    for benchmark_line in benchmark_lines:
        benchmark_line = benchmark_line.strip()
        if not benchmark_line:
            continue
        
        # 提取文件名
        filename = benchmark_line.strip()
        
        # 在目标文件中查找匹配项
        is_match = False
        matching_path = ""
        for target_line in target_lines:
            target_line = target_line.strip()
            if filename in target_line:
                is_match = True
                matching_path = target_line
                break
        
        # 保存匹配结果
        results.append((filename, matching_path, is_match))
    
    return results

def main():
    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print(f"Usage: python {sys.argv[0]} benchmark.csv target_file [output_file]")
        return
    
    benchmark_path = sys.argv[1]
    target_path = sys.argv[2]
    
    results = match_files(benchmark_path, target_path)
    
    # 如果指定了输出文件，则写入文件，否则打印到控制台
    if len(sys.argv) == 4:
        output_path = sys.argv[3]
        try:
            with open(output_path, 'w') as f:
                for filename, matching_path, is_match in results:
                    f.write(f"{filename},{matching_path},{str(is_match).lower()}\n")
            print(f"Results written to {output_path}")
        except IOError as e:
            print(f"Error writing to output file: {e}")
    else:
        # 输出匹配结果到控制台
        for filename, matching_path, is_match in results:
            print(f"{filename},{matching_path},{str(is_match).lower()}")

if __name__ == "__main__":
    main()
