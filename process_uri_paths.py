import csv

def remove_duplicate_paths(path):
    """检测并删除嵌套的重复路径"""
    if not path or '/' not in path:
        return path
    
    # 确保路径以/开头
    if not path.startswith('/'):
        path = '/' + path
    
    # 分割路径为段落
    segments = path.strip('/').split('/')
    
    # 检测并移除重复的路径段落
    i = 0
    cleaned_segments = []
    while i < len(segments):
        # 添加当前段落
        segment = segments[i]
        cleaned_segments.append(segment)
        
        # 寻找未来重复出现的相同段落
        j = i + 1
        while j < len(segments) and segments[j] == segment:
            j += 1
        
        # 如果找到重复段落，跳过它们
        if j > i + 1:
            i = j
        else:
            i += 1
    
    # 检测更复杂的重复模式（如 a/b/a/b/a/b）
    if len(cleaned_segments) >= 4:  # 需要至少4个段落才可能有有意义的重复
        for pattern_len in range(1, len(cleaned_segments) // 2 + 1):
            # 检查是否存在长度为pattern_len的重复模式
            first_pattern = cleaned_segments[:pattern_len]
            
            # 检查余下的段落是否都是这个模式的重复
            is_repeating = True
            for i in range(pattern_len, len(cleaned_segments), pattern_len):
                end = min(i + pattern_len, len(cleaned_segments))
                if cleaned_segments[i:end] != first_pattern[:end-i]:
                    is_repeating = False
                    break
            
            if is_repeating:
                cleaned_segments = first_pattern
                break
    
    # 重新组合路径
    return '/' + '/'.join(cleaned_segments)

def process_uri_paths(input_file, output_file):
    # 用于存储已处理过的路径
    unique_paths = set()
    result_paths = []
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        # 检查是否有标题行
        headers = next(reader, None)
        has_header = False
        
        # 如果第一行看起来像数据，将其处理
        if headers and any(h.find('/') >= 0 for h in headers):
            uri = headers[0].strip()
            
            # 提取第一个/及之后的内容
            if '/' in uri:
                path = uri[uri.find('/'):]  # 从第一个/开始截取
                # 移除嵌套的重复路径
                path = remove_duplicate_paths(path)
                
                if path and path not in unique_paths:
                    unique_paths.add(path)
                    result_paths.append(path)
        else:
            has_header = True
        
        # 处理其余行
        for row in reader:
            if not row or not row[0]:  # 跳过空行或第一列为空的行
                continue
            
            uri = row[0].strip()  # 获取URI并去除空白
            
            # 提取第一个/及之后的内容
            if '/' in uri:
                path = uri[uri.find('/'):]  # 从第一个/开始截取
            else:
                # 如果没有/，则添加一个/前缀
                path = '/' + uri
            
            # 移除嵌套的重复路径
            path = remove_duplicate_paths(path)
            
            # 检查是否已存在该路径
            if path and path not in unique_paths:
                unique_paths.add(path)
                result_paths.append(path)
    
    # 将不重复的路径写入输出文件
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        
        # 如果原文件有标题行且我们想保留相同格式，可以添加相应的标题
        if has_header:
            writer.writerow(["Unique_Path"])
            
        # 写入唯一路径
        for path in result_paths:
            writer.writerow([path])
    
    print(f"处理完成，共找到 {len(result_paths)} 条不重复路径")
    print(f"结果已保存到 {output_file}")

if __name__ == "__main__":
    input_file = "actions_uri.csv"
    output_file = "unique_paths.csv"
    process_uri_paths(input_file, output_file) 