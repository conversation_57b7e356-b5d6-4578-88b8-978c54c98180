部门,接口名,注释
租车技术,pay.alipay.sign.status.query,支付宝免密代扣签约状态查询
电池服务研发,switch.charge.map.querySiteDevicePort,查询充电站点包含可用格口信息
电池服务研发,switch.charge.map.queryDevicePort,查询设备包含可用格口信息
电池服务研发,switch.charge.map.queryUserSite,查询附近充电站点列表
金融,f.loan.page.rights.superMember,超级会员详情
校企合作平台,member.recharge,会员充值
校企合作平台,points.business.type.config,积分-业务类型配置
校企合作平台,member.recharge.qrCode,会员充值二维码
校企合作平台,dress.changing.image.segment.edit.result.query,换衣图片切分编辑结果查询
校企合作平台,dress.changing.image.segment.result.query,换衣图片切分结果查询
校企合作平台,dress.changing.image.segment.edit,换衣图片切分编辑
校企合作平台,dress.changing.example.image.fission,换衣工具裂变示例图
校企合作平台,dress.changing.image.segment.edit.result.save,换衣图片切分编辑结果保存
校企合作平台,dress.changing.example.image.image.processing,换衣工具图像处理示例图
校企合作平台,dress.changing.image.segmen.edit.result.query,换衣图片切分编辑结果查询
校企合作平台,dress.changing.task.update.result,换衣-算法侧接口更新结果
校企合作平台,dress.changing.task.edit,换衣任务编辑
校企合作平台,clothing.tools.property.data.count.query,服装工具资产日期数据量查询
校企合作平台,dress.changing.task.generate,换衣任务生成
校企合作平台,dress.changing.task.generate.again,换衣任务再次生成
校企合作平台,dress.changing.image.segment,换衣图片切分
校企合作平台,dress.changing.example.image.3d,换衣工具商拍3D示例图
校企合作平台,dress.changing.image.segmen.result.query,换衣图片切分结果查询
校企合作平台,dress.changing.image.segment.again,换衣图片切分重试
校企合作平台,dress.changing.example.image.3d.bodysuit,换衣工具商拍3D连体衣示例图
校企合作平台,dress.changing.task.detail,换衣任务明细
校企合作平台,download.zip.query,压缩下载进度查询
校企合作平台,dress.changing.example.image.mannequin,换衣工具商拍人台示例图
校企合作平台,dress.changing.image.background,换衣工具背景图片
校企合作平台,dress.changing.image.model,换衣工具模特图片
校企合作平台,dress.changing.task.page.query,换衣任务分页查询
校企合作平台,generating.tools.home.carousel.image,生成工具首页轮播图
校企合作平台,dress.changing.task.result.download.zip,换衣任务结果压缩下载
校企合作平台,dress.changing.task.vote.update,换衣任务投票状态更新
校企合作平台,dress.changing.image.model.enum,换衣工具模特图片枚举
校企合作平台,dress.changing.task.name.update,换衣任务名称更新
街猫研发,pet.home.query.entryFee.oredr.payStatus,查询宠托师认证费订单支付状态
校企合作平台,dress.changing.task.result.batch.delete,换衣任务结果批量删除
校企合作平台,dress.changing.task.result.batch.collection,换衣任务结果批量收藏
校企合作平台,clothing.tools.property.page.query,服装工具资产分页查询
校企合作平台,clothing.tools.home.carousel.image,服装工具首页轮播图
校企合作平台,clothing.tools.home.sample.image,服装工具首页样例图
普惠用车研发,hitch.passenger.closeChangeDriver,乘客发起终止更换司机
街猫研发,pet.ecommerce.user.grant.property,发放自营新人券
普惠用车研发,hitch.passenger.initChangeDriver,乘客发起更换司机
街猫研发,pet.user.ecommerce.channel.benefit.window,频道页券弹窗
街猫研发,pet.user.facade.ecommerce.getChannelResources,获取频道页资源
街猫研发,pet.user.facade.ecommerce.getChannelBrandList,客态频道页品牌
街猫研发,pet.user.facade.ecommerce.getChannelBrandList.login,主态频道页品牌
街猫研发,pet.user.facade.ecommerce.getChannelRecommendProductList,客态频道页推荐商品
街猫研发,pet.user.facade.ecommerce.getChannelRecommendProductList.login,主态频道页推荐商品
街猫研发,pet.user.facade.infoflow.getSurveyQuestions,查询问卷
街猫研发,pet.user.facade.batchAnswerSurveyQuestions,批量问答问卷问题
街猫研发,pet.user.facade.ecommerce.getChannelWaistSealResourcePosition,获取频道页腰封资源
街猫研发,pet.user.facade.infoflow.tightSelection,查询严选会场
街猫研发,pet.user.facade.infoflow.dailyTightSelection,获取信息流日常严选会场
电池服务研发,switchpower.user.queryUserHasEffectiveOrder,查询用户是否有有效的(待生效/已生效)订单(换电/租电)
租车技术,merchant.scan.order.query,近30天扫码订单数
街猫研发,pet.home.common.getCommonConfig,获取公共配置
电池服务研发,switchpower.user.change.preCheck,扫码换电前置校验
电池服务研发,switchpower.user.commodity.getHomeCommodityList,新的获得租电列表页
电池服务研发,switchpower.merchant.serviceStation.belongStation.query,车服门店-小哈服务站搜索归属服务站
电池服务研发,switchpower.merchant.serviceStation.apply.cancel,车服门店-门店审批撤回操作
普惠用车研发,carpool.HighwayFeeVerifyPage.ClickNoPaymentRequired,高速费核实页点击无需收取
电池服务研发,switchpower.merchant.serviceStation.merchant.partner,车服门店-通过商户id查询合作的经营商家
电池服务研发,switchpower.merchant.serviceStation.button.show,车服门店-是否展示新增按钮
电池服务研发,switchpower.user.shop.user.getStationShopInfoList,查询服务站门店信息
电池服务研发,switchpower.gray.station.stationGray,服务站查询是否是灰度门店
用户平台技术部,homepage.welfare.popupWindow,首页福利中心聚合弹窗
用户平台技术部,homepage.skin.module.query,查询首页皮肤信息
两轮出行研发,offline.app.package.list,查询App离线包
电池服务研发,switchpower.serviceStation.shop.getShopByLocation,小哈服务站根据城市与经纬度获取门店
电池服务研发,switchpower.innovateCommodity.query.commodityGroupSpu,按spuId分组查询最低价格商品列表
电池服务研发,switchpower.innovateCommodity.query.queryCommodityInShopList,查询商品所在门店信息
电池服务研发,switchpower.innovateCommodity.query.commodityList,小哈服务站C端首页租车商品列表
电池服务研发,switchpower.innovate.areainfo.getCityInitials,首字母城市列表
两轮出行研发,battery.cabinet.openCabinetLock,开启电柜总的仓门
电池服务研发,switchpower.user.getCommodityDetail,查询套餐 详情
租车技术,goods.grade.sell.query.queryDetailByGoodsId,根据商品ID查询商品信息
租车技术,goods.grade.sell.query.page,商品分级售卖列表查询
租车技术,goods.grade.sell.switch,开启/关闭分级售卖
租车技术,goods.grade.sell.price.query,实时查询分级售卖分级价格)
租车技术,goods.grade.sell.modify,修改车型售卖商品
普惠用车研发,hitch.common.configInfoCombined.cdn,获取多业务线首页配置和cdn
普惠用车研发,hitch.deliver.common.configInfo,顺风送首页配置
租车技术,vehicle.license.price.list,车型车牌下单报价列表
街猫研发,pet.ecommerce.product.find,商品准确查询
街猫研发,pet.ecommerce.product.search.url.exact,链接精准搜索商品
电池服务研发,switchpower.gray.station.stationCityGray,城市灰度接口
租车技术,vehicle.more.group.list,派单分组列表
街猫研发,pet.ecommerce.product.search.url,链接精准搜索商品
用户平台技术部,aquarius.mall.recommendItemList,[勋章商城]查询推荐商品列表
人工智能部,aichat.goods.purchase,订单确认
用户平台技术部,aquarius.mall.itemDetail,[勋章商城]查询商品详情
用户平台技术部,aquarius.mall.addressChangeCheck,[勋章商城]检查是否允许修改订单收货地址
普惠用车研发,hitch.driver.pool.entry,详情页沿途拼车详情卡片
普惠用车研发,hitch.CarpoolAlongMatchPage.close,沿途拼车匹配页面抢单关闭
普惠用车研发,hitch.CarpoolAlongMatchPage.PageInitFunction,拼车匹配页面顶部初始化
普惠用车研发,hitch.CarpoolAlongMatchPage.open,沿途拼车匹配页面抢单开启
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.open,沿途拼车抢单设置页面开启抢单
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.modify,沿途拼车抢单设置页面修改抢单信息
普惠用车研发,hitch.CarpoolAlongMatchPage.refresh,沿途拼车再拼一单页面刷新
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.close,沿途拼车抢单设置页面关闭抢单
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.PageInitFunction,抢单设置页面初始化
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.verify,沿途拼车抢单设置页面保存参数校验
普惠用车研发,hitch.driver.score.isV2Hit,查询司机是否命中新版本
普惠用车研发,hitch.driver.score.getPerformanceScoreConfig,查看履约分规则
普惠用车研发,hitch.CarpoolAutoReceiveOrderSettingsPage.refresh,沿途拼抢单设置页面刷新
普惠用车研发,carpool.CarpoolDriverHomePage.RefreshHomeTips,拼车车主首页刷新新手引导
用户平台技术部,aquarius.mall.orderInfo,[勋章商城-印鸽]查询订单信息
用户平台技术部,aquarius.mall.createOrder,[勋章商城]创建订单
用户平台技术部,aquarius.mall.logisticsNotice,[勋章商城-印鸽]物流通知
用户平台技术部,aquarius.mall.orderInterceptNotice,[勋章商城-印鸽]订单拦截通知
街猫研发,pet.home.order.goPay,宠爱到家订单去支付
客户服务平台,cs.chat.syncSwitchOrder,同步切换订单的信息
街猫研发,pet.home.sitter.sitterList,爱宠侠列表
街猫研发,pet.home.order.userOrderDetail,爱宠到家家长订单详情
街猫研发,pet.home.rule.billingRule,获取计费规则
普惠用车研发,hitch.journey.closeAutoReceive,沿途拼抢单任务关闭接口
街猫研发,pet.home.order.orderUserCancel,家长取消订单
街猫研发,pet.home.address.homeGetDefaultAddress,首页查询默认地址
街猫研发,pet.home.order.refuseOrder,宠托师拒绝接单
街猫研发,pet.home.common.getBannerConfig,查询banner图
街猫研发,pet.home.order.orderCalc,宠物到家订单试算
街猫研发,pet.home.sitter.queryPriceCalendar,查询价格日历
街猫研发,pet.home.order.confirmOrder,宠托师确认接单
街猫研发,pet.home.order.saveOrder,宠物到家下单接口
街猫研发,pet.home.order.queryUserOrderList,获取家长订单列表
街猫研发,pet.home.sitter.querySitterDetail,爱宠师详情
街猫研发,pet.home.order.queryPetSitterOrderList,获取爱宠侠订单列表
街猫研发,pet.home.order.getOrderStatus,获取订单支付状态
街猫研发,pet.home.msg.privateChat,爱宠到家私聊接口
街猫研发,pet.home.sitter.getAddressList,查询爱宠侠地址列表(工作台)
街猫研发,pet.home.sitter.updateAddress,修改爱宠侠地址（工作台）
街猫研发,pet.home.sitter.querySitterInfo,查询宠托师信息（工作台）
街猫研发,pet.home.sitter.updateSitterInfo,基础信息修改（工作台）
街猫研发,pet.home.sitter.updaterServiceTypes,修改爱宠侠服务项目(工作台)
街猫研发,pet.home.sitter.queryServiceTypes,查询爱宠侠服务项目（工作台）
街猫研发,pet.home.sitter.querySitterServices,查询爱宠侠服务项目（工作台）
街猫研发,pet.home.order.orderPetCheck,宠爱到家下单宠物订单校验
街猫研发,pet.home.sitter.queryBasicSitterInfo,查询爱宠侠基础信息（工作台）
街猫研发,pet.home.common.getPetSitterConfig,查询爱宠侠配置
街猫研发,pet.home.sitter.updateBasicSitterInfo,更新爱宠侠头像昵称（工作台）
街猫研发,pet.home.sitter.isPetSitter,是否是爱宠侠
街猫研发,pet.home.order.queryOrderDetailForWait,抢单大厅订单详情查询
街猫研发,pet.home.order.queryOrderDetailForPetSitter,服务大厅订单详情
街猫研发,pet.home.order.orderSitterCancel,爱宠侠取消订单
街猫研发,pet.home.order.queryOrderCountDetail,查询爱宠侠订单统计(工作台)
街猫研发,pet.home.order.takeOrder,宠物师抢单
街猫研发,pet.home.order.takeOrderList,获取待抢订单列表
街猫研发,pet.home.order.queryPromiseDetail,履约单详情
街猫研发,pet.home.promise.savePromiseRecordData,履约打卡
街猫研发,pet.home.sitter.queryUserAccountBalance,工作台查询余额接口
电池服务研发,switchpower.merchant.station.allocate.in.shop,商户端-根据调出门店获取调入门店接口
街猫研发,pet.home.promise.queryPromiseRecordList,服务记录列表
金融,f.tempAmount.apply,临时额度申请
电池服务研发,switchpower.merchant.station.board.queryVehicleBoardNew,商户端车辆数据看板
金融,f.tempAmount.query,临时额度下发状态查询
租车技术,ts.return.car.charge.fee.list,分时计算还车收费
电动车研发,rent.lease.warranty.check.list,智能芯核对列表
电动车研发,rent.lease.warranty.confirm.delivery.detail,智能芯确认寄出详情
电动车研发,rent.lease.warranty.check.record,智能芯核对记录
共享平台,inspire.grid.list,激励宫格列表
共享平台,insprie.rebate.click.url,激励返币推广链接获取
共享平台,inspire.user.auth.url,生成授权链接
共享平台,inspire.user.binding,电商用户绑定
共享平台,inspire.rebate.order.list,激励返币订单列表
共享平台,inspire.goods.link,生成商品跳转链接
共享平台,inspire.user.auth.search,授权查询
共享平台,inspire.rebate.item.search,激励返币商品搜索
共享平台,inspire.rebate.notice.recall,用户返币通知回调
共享平台,inspire.rebate.notice,用户返币通知
共享平台,inspire.rebate.account,激励返币账户查询
共享平台,inspire.page.config,激励页面配置下发
普惠用车研发,taxi.public.review.save,经济车保存大众评审
普惠用车研发,taxi.driver.ridehailing.journeyList,打车车主选单大厅
普惠用车研发,taxi.driver.ridehailing.arrivePaxStartPlace,达到上车点（rideHail）
普惠用车研发,taxi.driver.ridehailing.receiveOrder,打车司机接单
普惠用车研发,taxi.driver.ridehailing.pkChannelOrder,打车pk接单
普惠用车研发,ecotaxi.EcoDriverOrderDetail.ReportRemainDistance,车主履约详情页上报剩余距离
普惠用车研发,ecotaxi.EcoDriverOrderDetail.CallVirtualPhone,车主履约详情页联系乘客
普惠用车研发,ecotaxi.EcoDriverOrderDetail.Refresh,车主履约详情页刷新页面
普惠用车研发,ecotaxi.EcoDriverOrderDetail.ConfirmGetOnCar,车主履约详情页乘客上车
普惠用车研发,ecotaxi.EcoDriverOrderDetail.ArrivePaxStartPlace,车主履约详情页到达乘客起点
普惠用车研发,ecotaxi.EcoDriverOrderDetail.OpenIM,车主履约详情页打开IM
普惠用车研发,ecotaxi.EcoDriverOrderDetail.AllocationOrder,车主履约详情页听单轮询
普惠用车研发,ecotaxi.EcoDriverOrderDetail.RejectOrder,车主履约详情页拒单
普惠用车研发,ecotaxi.EcoDriverOrderDetail.GetPaxLocation,车主履约详情页获取乘客位置
普惠用车研发,ecotaxi.EcoDriverOrderDetail.PollOrderStatus,车主履约详情页轮询订单状态
普惠用车研发,ecotaxi.EcoDriverOrderDetail.ArriveDestination,车主履约详情页到达目的地
普惠用车研发,ecotaxi.EcoDriverOrderDetail.RemindWillArriveDestination,车主履约详情页提醒乘客到达终点
普惠用车研发,ecotaxi.EcoDriverOrderDetail.RefuseModifyDestination,车主履约详情页拒绝修改目的地
普惠用车研发,ecotaxi.EcoDriverOrderDetail.CancelOrder,车主履约详情页取消订单
普惠用车研发,ecotaxi.EcoDriverOrderDetail.GuideDriverContactPax,车主履约详情页引导司机联系乘客
普惠用车研发,ecotaxi.EcoDriverOrderDetail.SubmitInfoCollectionQA,车主履约详情页提交信息采集
普惠用车研发,ecotaxi.EcoDriverOrderDetail.OpenSecurityCenter,车主履约详情页安全中心
普惠用车研发,ecotaxi.EcoDriverOrderDetail.AcceptOrder,车主履约详情页接单
普惠用车研发,ecotaxi.EcoDriverOrderDetail.AcceptModifyDestination,车主履约详情页同意修改目的地
租车技术,saas.car.damage.judge.gray,车损灰度校验
租车技术,saas.car.damage.type.config.list,损类型配置
租车技术,merchant.pause.fee.cal.new,停运费计算
租车技术,saas.car.damage.assessment.standard,查询车损评估标准
共享平台,coupon.order.limit.batch,批量查询限购
普惠用车研发,taxi.driver.journeyList,哈啰打车选单列表
租车技术,merchant.order.deposit.supplement,预估费用扣款补充凭证
共享平台,mars.behavior.reporting,用户行为上报
租车技术,agg.supplierDeduct.payNo.query,根据商家扣款流水号批量获取支付凭证
街猫研发,pet.user.getUserSpringActivityInfo,查询用户春节活动资格
街猫研发,pet.user.drawSpringCard,领取春节饭卡
街猫研发,pet.user.getUserAnnualReport,查询用户年报数据
租车技术,agg.create.demand.order,创建需求单
租车技术,agg.demand.order.add.vehicle,需求单追加车型
租车技术,agg.demand.order.cancel,取消需求单
租车技术,agg.demand.order.adjust.price,需求单加价
租车技术,agg.demand.order.vehicle.requirement.config,车型附加要求查询接口
租车技术,agg.demand.order.deatil,需求单订单详情
租车技术,agg.demand.order.cancel.and.again.create,取消需求单并重新发单
租车技术,agg.demand.order.plat.default.service.query,平台基础保障查询接口
租车技术,supply.demand.order.list,抢单列表
租车技术,demand.order.mismatch.add,添加需求单不匹配记录
租车技术,order.vehicle.damage.insurance.process,车损处理流程接口
租车技术,demand.order.settle.detail,抢单 查询可排车商品库存列表（app）
租车技术,demand.order.confirm.grab.order,需求单抢单
租车技术,demand.order.selectable.site,查询需求单可以选择的门店信息
普惠用车研发,hitch.driver.publishJourney.nonAuth,已登录未认证车主发单
普惠用车研发,hitch.driver.rePublishJourney.nonAuth,已登录未认证车主重发单
租车技术,supply.demand.order.detail,抢单详情
租车技术,supply.grab.order.city,可抢单城市
普惠用车研发,hitch.core.checkJourneyListV2.nonAuth,车主待处理行程列表（车主未认证）
普惠用车研发,hitch.driver.discoverV2.nonAuth,首页3.0聚合页（车主未认证）
租车技术,supply.grab.order.hall,可抢大厅显示
普惠用车研发,hitch.driver.refinedPickCondition.nonAuth,未认证车主精筛
普惠用车研发,hitch.driver.fastPickCondition.nonAuth,未认证车主快筛
普惠用车研发,hitch.biz.getRecommendDestinations.nonAuth,获取车主首页推荐目的地（车主未认证）
普惠用车研发,hitch.driver.banPageMsg.nonAuth,车主封禁页数据（车主未认证）
普惠用车研发,hitch.driver.preJourneyDetailV2.nonAuth,未认证车主接单前乘客详情页
普惠用车研发,hitch.driver.journeyListV2.nonAuth,司机临时行程选单（未认证）
普惠用车研发,hitch.passenger.thankFeeConfig,顺风车查询感谢费配置
共享平台,user.auth.checkAliPayIdLogin,user.auth.checkAliPayIdLogin
普惠用车研发,hitch.driver.journeyList.nonAuth,司机附近找单同跨城（未认证）
租车技术,saas.goods.cancel.template.create.new,【规则关联】取消模版创建-新
租车技术,saas.goods.relate.template,【规则关联】商品绑定规则
租车技术,saas.goods.relate.template.list,【规则关联】商品绑定规则列表
租车技术,saas.goods.due.template.create.new,【规则关联】预定模版创建-新
租车技术,saas.goods.policy.template.create.new,【规则关联】服务模版创建-新
电动车研发,rent.lease.useBike.tab.type,查询底部用车tab展示类型
普惠用车研发,hitch.driver.yearly.report,车主年度报告
用户平台技术部,platform.common.launch.undertask.shunt,投放链路投承
街猫研发,pet.hone.promise.getLiveVideoInfoForPetSitter,获取宠物师直播信息
街猫研发,pet.home.live.getLivePullUrl,到家直播-获取拉流地址
街猫研发,pet.hone.promise.getLiveVideoInfoForUser,获取用户直播信息
街猫研发,pet.home.live.getLivePushUrl,到家直播-获取推流地址
租车技术,saas.ge.goods.list,【政企】可报名商品列表pc
租车技术,merchant.enterprise.create.apply,商户报名政企
租车技术,saas.ge.goods.modify.status,【政企】商品报名状态改变pc
租车技术,app.ge.goods.modify.status,【政企】报名商品状态改变app
租车技术,app.ge.goods.list,【政企】商品报名列表app
租车技术,saas.goods.sub.card.enrollment.log,【次卡价格授权优化】
客户服务平台,css.workbench.filterAbuseTalk,辱骂话术过滤
租车技术,merchant.enterprise.cancel.apply,取消商户审核
租车技术,merchant.enterprise.query.detail,政企商户查询
租车技术,merchant.car.oil.box.update,商家端APP修改车机上报油箱最大容量
租车技术,saas.goods.sub.card.goods.list,【次卡价格授权优化】商品列表
普惠用车研发,deliver.query.order.for.other.pay,代付新增查询订单信息接口
普惠用车研发,taxi.driver.receiveOrder,哈啰打车车主接单
租车技术,user.enterprise.info,政企查询用户信息
普惠用车研发,taxi.driver.arrivePaxStartPlace,车主到达乘客起点
租车技术,user.enterprise.payType,政企查询支付类型
普惠用车研发,taxi.driver.pkChannelOrder,司机pk渠道接单
电池服务研发,switchpower.user.nearby.dealer,查询附近网点(兼容充换)
电池服务研发,switchpower.user.dealer.detail,查询网点详情(兼容充换)
电池服务研发,switchpower.user.cabinet.grid.detail,电柜格口详情(兼容充换柜)
电池服务研发,switchpower.user.deviceInfo,获取设备类型
电池服务研发,switchpower.user.cs.pageBooting,可充可换引导页渲染接口
电池服务研发,switchpower.user.getChargeCabinet,获取充电柜信息
校企合作平台,dress.changing.common.reference.images.save.batch,批量保存用户常用参考图
校企合作平台,dress.changing.common.reference.images.delete.batch,批量删除用户常用参考图
校企合作平台,dress.changing.common.reference.images.query.page,分页查询用户常用参考图
校企合作平台,dress.changing.result.feedback.record.query.page,分页查询换衣结果反馈记录
校企合作平台,dress.changing.result.download.record.query.page,分页查询换衣结果下载记录
校企合作平台,dress.changing.result.feedback.save,换衣结果保存反馈
校企合作平台,dress.changing.result.feedback.query,换衣结果反馈查询
校企合作平台,clothing.tools.handle.type.enum.query,服装工具处理类型查询
客户服务平台,css.workbench.generateServiceDialogId,非进线场景生成对话ID
两轮出行研发,user.ride.detail,用户订单详情-老
人工智能部,aichat.deleteDislikeRecord,点赞点踩取消
人工智能部,aichat.recordLikeAndDislike,点赞点踩记录
电池服务研发,/switchpower/energy/siteListByInstall,设备安装 获取站点列表(过滤可换可充站点)
人工智能部,aichat.addDislikeReason,不喜欢原因记录
电池服务研发,switchpower.merchant.shop.distribution.queryByShop,门店分润配置规则查看
电动车研发,rent.merchant.bike.track.list,车辆轨迹列表接口
电池服务研发,switchpower.merchant.shop.distribution.update,门店分润配置规则编辑
电动车研发,rent.merchant.bike.track.query.point,查询车辆轨迹点位
街猫研发,pet.home.sitter.getShareCodeMsg,获取分享口令码
街猫研发,pet.home.sitter.getUrlByCode,根据口令码查询宠物师详情链接
电动车研发,rent.merchant.bike.track.query.order,查询轨迹关联订单
