import requests
import json


def main():
    url = "https://aibrain-large-model.hellobike.cn/AIBrainLmp/api/v1/runLargeModelApplication/run"

    data = {
        'prompt': '介绍一下世界杯',
        'agentId': '1234015687568515072',
        'secretKey': 'sk-_DQsFPo7tjnOln8n9U5W7cUG_EzwVOk3PgBTSSARWU8',
        'serviceName': 'Deepseek-reasoner',
        'stream': True
    }

    headers = {
        'Content-Type': 'application/json'
    }
    
    response = requests.request("POST", url, headers=headers, data=json.dumps(data))
    
    if response.status_code == 200:
        print(response.text)
    else:
        print(f"HTTP请求失败，状态码：{response.status_code}")
    

if __name__ == '__main__':
    main()