import requests
import time
import csv
import json

# 配置常量
API_URL = "https://proapi.pet-bang.cn/public/pagePetMaster"
HEADERS = {
    "Host": "proapi.pet-bang.cn",
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjZiODY1ZTA0LTdmYWItNGNhMi1iOTQzLTJkZDExNzk2MDU3MSJ9.DhtESa_kVqQ4i6krWgvuPw7gUUMGdnD7ZyuIeHgU0TuffP3q5b6akjs88rSLDmNKuqXFWNZ_1OW4po55p3YA1Q",
    "Accept-Encoding": "gzip, deflate, br",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.56(0x1800383b) NetType/WIFI Language/zh_CN",
    "Referer": "https://servicewechat.com/wx4c78b82e495f7950/32/page-frame.html"
}

# 请求参数
PARAMS = {
    "type": "",
    "longitude": "120.1127437182269",
    "latitude": "30.28911551394019",
    "ratioStart": "",
    "ratioEnd": "",
    "distanceStart": "",
    "distanceEnd": "",
    "priceStart": "",
    "priceEnd": "",
    "expStart": "",
    "expEnd": "",
    "orderByColumn": "juli",
    "isAsc": "ascending"
}

def fetch_all_pet_masters():
    page_num = 1
    all_pet_masters = []
    
    while True:
        try:
            # 更新分页参数
            current_params = PARAMS.copy()
            current_params["pageNum"] = page_num
            
            # 发送请求
            response = requests.get(
                API_URL,
                headers=HEADERS,
                params=current_params,
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"Page {page_num} 请求失败，状态码：{response.status_code}")
                break
                
            data = response.json()
            if data.get('code') != 200:
                print(f"Page {page_num} API返回错误：{data.get('msg')}")
                break
            
            # 从响应中提取数据
            pet_masters = data.get('rows', [])
            if not pet_masters:
                print("已获取所有数据")
                break
            
            # 提取所需字段
            for pet_master in pet_masters:
                extracted_data = {
                    "name": pet_master.get("name", ""),
                    "address": pet_master.get("address", ""),
                    "phone": pet_master.get("phone", "")
                }
                all_pet_masters.append(extracted_data)
            
            print(f"Page {page_num} 完成，获取到 {len(pet_masters)} 条数据")
            page_num += 1
            
            # 判断是否还有下一页
            total = data.get('total', 0)
            page_size = data.get('pageSize', 10)
            if page_num > (total + page_size - 1) // page_size:
                print("已到达最后一页")
                break
            
            # 请求间隔，避免过于频繁
            time.sleep(2)
            
        except requests.exceptions.RequestException as e:
            print(f"请求异常：{str(e)}")
            time.sleep(5)  # 出错后等待更长时间
            continue
        except Exception as e:
            print(f"处理异常：{str(e)}")
            break
    
    # 将数据保存到CSV文件
    save_to_csv(all_pet_masters)
    
    return all_pet_masters

def save_to_csv(data):
    if not data:
        print("没有数据可保存")
        return
    
    # 确定CSV文件中的字段
    fieldnames = ["name", "address", "phone"]
    
    # 写入CSV文件
    with open('pet_masters.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"数据已保存到 pet_masters.csv，共 {len(data)} 条记录")

if __name__ == "__main__":
    fetch_all_pet_masters() 